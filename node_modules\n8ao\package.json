{"name": "n8ao", "version": "1.10.0", "description": "An efficient and visually pleasing implementation of SSAO with an emphasis on temporal stability and artist control.", "main": "dist/N8AO.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "rm -rf .parcel-cache && parcel build src/N8AOPass.js && cp dist/N8AO.js example/N8AO.js && cp dist/N8AO.js.map example/N8AO.js.map && cp dist/N8AO.js example_postprocessing/N8AO.js && cp dist/N8AO.js.map example_postprocessing/N8AO.js.map", "dev": "nodemon --watch src --ext .js --exec 'npm run build' & nodemon dev-server.js"}, "repository": {"type": "git", "url": "git+https://github.com/N8python/n8ao.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/N8python/n8ao/issues"}, "homepage": "https://github.com/N8python/n8ao#readme", "targets": {"main": {}}, "peerDependencies": {"postprocessing": ">=6.30.0", "three": ">=0.137"}, "type": "module", "devDependencies": {"cors": "^2.8.5", "express": "^4.18.2", "live-server": "^1.1.0", "nodemon": "^3.1.0", "parcel": "^2.8.3", "servez": "^1.14.2"}}