import * as THREE from 'three';
import { BlendFunction, Effect } from 'postprocessing';
type LensFlareEffectOptions = {
    /** The blend function of this effect */
    blendFunction: BlendFunction;
    /** Boolean to enable/disable the effect */
    enabled: boolean;
    /** The glare size */
    glareSize: number;
    /** The position of the lens flare in 3d space */
    lensPosition: THREE.Vector3;
    /** Effect resolution */
    screenRes: THREE.Vector2;
    /** The number of points for the star */
    starPoints: number;
    /** The flare side */
    flareSize: number;
    /** The flare animation speed */
    flareSpeed: number;
    /** Changes the appearance to anamorphic */
    flareShape: number;
    /** Animated flare */
    animated: boolean;
    /** Set the appearance to full anamorphic */
    anamorphic: boolean;
    /** Set the color gain for the lens flare. Must be a THREE.Color in RBG format */
    colorGain: THREE.Color;
    /** Texture to be used as color dirt for starburst effect */
    lensDirtTexture: THREE.Texture | null;
    /** The halo scale */
    haloScale: number;
    /** Option to enable/disable secondary ghosts */
    secondaryGhosts: boolean;
    /** Option to enable/disable aditional streaks */
    aditionalStreaks: boolean;
    /** Option to enable/disable secondary ghosts */
    ghostScale: number;
    /** TODO The opacity for this effect */
    opacity: number;
    /** Boolean to enable/disable the start burst effect. Can be disabled to improve performance */
    starBurst: boolean;
};
export declare class LensFlareEffect extends Effect {
    constructor({ blendFunction, enabled, glareSize, lensPosition, screenRes, starPoints, flareSize, flareSpeed, flareShape, animated, anamorphic, colorGain, lensDirtTexture, haloScale, secondaryGhosts, aditionalStreaks, ghostScale, opacity, starBurst, }: LensFlareEffectOptions);
    update(_renderer: any, _inputBuffer: any, deltaTime: number): void;
}
type LensFlareProps = {
    /** Position of the effect */
    lensPosition?: THREE.Vector3;
    /** The time that it takes to fade the occlusion */
    smoothTime?: number;
} & Partial<LensFlareEffectOptions>;
export declare const LensFlare: ({ smoothTime, blendFunction, enabled, glareSize, lensPosition, screenRes, starPoints, flareSize, flareSpeed, flareShape, animated, anamorphic, colorGain, lensDirtTexture, haloScale, secondaryGhosts, aditionalStreaks, ghostScale, opacity, starBurst, }: LensFlareProps) => import("react/jsx-runtime").JSX.Element;
export {};
