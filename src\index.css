@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Cyberpunk Color Palette */
  --neon-cyan: #00ffff;
  --neon-pink: #ff0080;
  --neon-purple: #8000ff;
  --neon-green: #00ff41;
  --neon-orange: #ff8000;
  --neon-blue: #0080ff;
  --dark-bg: #0a0a0f;
  --darker-bg: #050508;
  --grid-color: rgba(0, 255, 255, 0.1);
}

body {
  margin: 0;
  min-height: 100vh;
  overflow-x: hidden;
  background: var(--dark-bg);
  cursor: none; /* Custom cursor */
}

html {
  scroll-behavior: smooth;
}

/* Custom Cyberpunk Cursor */
.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, var(--neon-cyan) 0%, transparent 70%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
  transition: transform 0.1s ease;
}

.custom-cursor::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  width: 40px;
  height: 40px;
  border: 2px solid var(--neon-cyan);
  border-radius: 50%;
  opacity: 0.3;
  animation: cursorPulse 2s infinite;
}

@keyframes cursorPulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.2); opacity: 0.6; }
}

/* Cyberpunk Scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: var(--darker-bg);
  border-left: 1px solid var(--neon-cyan);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--neon-cyan), var(--neon-purple));
  border-radius: 6px;
  box-shadow: 0 0 10px var(--neon-cyan);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--neon-pink), var(--neon-orange));
  box-shadow: 0 0 20px var(--neon-pink);
}

/* Cyberpunk Grid Background */
.cyber-grid {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(var(--grid-color) 1px, transparent 1px),
    linear-gradient(90deg, var(--grid-color) 1px, transparent 1px);
  background-size: 50px 50px;
  z-index: -2;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* Holographic Effects */
.holographic {
  background: linear-gradient(45deg,
    transparent 30%,
    rgba(0, 255, 255, 0.1) 50%,
    transparent 70%);
  background-size: 200% 200%;
  animation: holographicShift 3s ease-in-out infinite;
  position: relative;
}

.holographic::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    transparent,
    rgba(255, 0, 128, 0.1),
    transparent);
  animation: holographicShift 2s ease-in-out infinite reverse;
}

@keyframes holographicShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Neon Glow Effects */
.neon-text {
  font-family: 'Orbitron', monospace;
  text-shadow:
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor,
    0 0 20px var(--neon-cyan),
    0 0 35px var(--neon-cyan),
    0 0 40px var(--neon-cyan);
  animation: neonFlicker 2s infinite alternate;
}

@keyframes neonFlicker {
  0%, 18%, 22%, 25%, 53%, 57%, 100% {
    text-shadow:
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor,
      0 0 20px var(--neon-cyan),
      0 0 35px var(--neon-cyan),
      0 0 40px var(--neon-cyan);
  }
  20%, 24%, 55% {
    text-shadow: none;
  }
}

.neon-border {
  border: 2px solid var(--neon-cyan);
  box-shadow:
    0 0 10px var(--neon-cyan),
    inset 0 0 10px rgba(0, 255, 255, 0.1);
  animation: neonBorderPulse 2s ease-in-out infinite alternate;
}

@keyframes neonBorderPulse {
  0% {
    box-shadow:
      0 0 10px var(--neon-cyan),
      inset 0 0 10px rgba(0, 255, 255, 0.1);
  }
  100% {
    box-shadow:
      0 0 20px var(--neon-cyan),
      0 0 30px var(--neon-cyan),
      inset 0 0 20px rgba(0, 255, 255, 0.2);
  }
}

/* Glitch Effects */
.glitch {
  position: relative;
  animation: glitch 2s infinite;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  animation: glitchTop 1s infinite;
  clip-path: polygon(0 0, 100% 0, 100% 33%, 0 33%);
  transform: translate(-2px);
}

.glitch::after {
  animation: glitchBottom 1.5s infinite;
  clip-path: polygon(0 67%, 100% 67%, 100% 100%, 0 100%);
  transform: translate(2px);
}

@keyframes glitch {
  0%, 14%, 15%, 49%, 50%, 99%, 100% {
    transform: translate(0);
  }
  15%, 49% {
    transform: translate(-2px, 2px);
  }
  50%, 99% {
    transform: translate(2px, -2px);
  }
}

@keyframes glitchTop {
  0%, 14%, 15%, 49%, 50%, 99%, 100% {
    transform: translate(0);
  }
  15%, 49% {
    transform: translate(-2px);
  }
}

@keyframes glitchBottom {
  0%, 14%, 15%, 49%, 50%, 99%, 100% {
    transform: translate(0);
  }
  50%, 99% {
    transform: translate(2px);
  }
}

/* Matrix Code Rain Effect */
.matrix-rain {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.matrix-column {
  position: absolute;
  top: -100%;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: var(--neon-green);
  text-shadow: 0 0 5px var(--neon-green);
  animation: matrixFall linear infinite;
}

@keyframes matrixFall {
  to {
    transform: translateY(100vh);
  }
}

/* Cyberpunk HUD Elements */
.hud-element {
  position: relative;
  background: rgba(0, 255, 255, 0.05);
  border: 1px solid var(--neon-cyan);
  backdrop-filter: blur(10px);
  clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 20px, 100% 100%, 20px 100%, 0 calc(100% - 20px));
}

.hud-element::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--neon-cyan), var(--neon-purple), var(--neon-pink));
  z-index: -1;
  clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 20px, 100% 100%, 20px 100%, 0 calc(100% - 20px));
  animation: hudGlow 3s ease-in-out infinite alternate;
}

@keyframes hudGlow {
  0% { opacity: 0.3; }
  100% { opacity: 0.8; }
}

/* Futuristic Gradient Text */
.cyber-gradient-text {
  background: linear-gradient(45deg,
    var(--neon-cyan),
    var(--neon-purple),
    var(--neon-pink),
    var(--neon-orange),
    var(--neon-green));
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: cyberGradientShift 4s ease-in-out infinite;
  font-family: 'Orbitron', monospace;
  font-weight: 800;
  text-transform: uppercase;
  letter-spacing: 2px;
}

@keyframes cyberGradientShift {
  0%, 100% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
}

/* 3D Floating Cards */
.floating-card {
  transform-style: preserve-3d;
  transition: all 0.3s ease;
  animation: cardFloat 6s ease-in-out infinite;
}

.floating-card:hover {
  transform: rotateY(10deg) rotateX(5deg) translateZ(20px);
}

@keyframes cardFloat {
  0%, 100% { transform: translateY(0px) rotateX(0deg); }
  33% { transform: translateY(-10px) rotateX(2deg); }
  66% { transform: translateY(5px) rotateX(-1deg); }
}

/* Neural Network Animation */
.neural-network {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.neural-node {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--neon-cyan);
  border-radius: 50%;
  box-shadow: 0 0 10px var(--neon-cyan);
  animation: neuralPulse 2s ease-in-out infinite;
}

.neural-connection {
  position: absolute;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--neon-cyan), transparent);
  animation: neuralFlow 3s linear infinite;
}

@keyframes neuralPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.5);
    opacity: 1;
  }
}

@keyframes neuralFlow {
  0% { opacity: 0; transform: scaleX(0); }
  50% { opacity: 1; transform: scaleX(1); }
  100% { opacity: 0; transform: scaleX(0); }
}

/* Hologram Scan Lines */
.hologram-scanlines {
  position: relative;
  overflow: hidden;
}

.hologram-scanlines::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 2px,
    rgba(0, 255, 255, 0.1) 2px,
    rgba(0, 255, 255, 0.1) 4px
  );
  animation: scanlineMove 2s linear infinite;
  pointer-events: none;
}

@keyframes scanlineMove {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100%); }
}

/* Energy Orb Effect */
.energy-orb {
  position: relative;
  border-radius: 50%;
  background: radial-gradient(circle, var(--neon-cyan) 0%, transparent 70%);
  animation: energyPulse 2s ease-in-out infinite;
}

.energy-orb::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  border-radius: 50%;
  background: radial-gradient(circle, transparent 40%, var(--neon-cyan) 50%, transparent 60%);
  animation: energyRing 3s linear infinite;
}

@keyframes energyPulse {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.2); opacity: 1; }
}

@keyframes energyRing {
  0% { transform: rotate(0deg) scale(0.5); opacity: 1; }
  100% { transform: rotate(360deg) scale(1.5); opacity: 0; }
}
