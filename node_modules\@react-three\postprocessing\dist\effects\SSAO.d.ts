/// <reference types="react" />
import { SSAOEffect, BlendFunction } from 'postprocessing';
export declare const SSAO: import("react").ForwardRefExoticComponent<{
    blendFunction?: BlendFunction | undefined;
    distanceScaling?: boolean | undefined;
    depthAwareUpsampling?: boolean | undefined;
    normalDepthBuffer?: import("three").Texture | undefined;
    samples?: number | undefined;
    rings?: number | undefined;
    worldDistanceThreshold?: number | undefined;
    worldDistanceFalloff?: number | undefined;
    worldProximityThreshold?: number | undefined;
    worldProximityFalloff?: number | undefined;
    distanceThreshold?: number | undefined;
    distanceFalloff?: number | undefined;
    rangeThreshold?: number | undefined;
    rangeFalloff?: number | undefined;
    minRadiusScale?: number | undefined;
    luminanceInfluence?: number | undefined;
    radius?: number | undefined;
    intensity?: number | undefined;
    bias?: number | undefined;
    fade?: number | undefined;
    color?: import("three").Color | undefined;
    resolutionScale?: number | undefined;
    resolutionX?: number | undefined;
    resolutionY?: number | undefined;
    width?: number | undefined;
    height?: number | undefined;
} & import("react").RefAttributes<SSAOEffect>>;
