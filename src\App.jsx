import { useState, useEffect, useRef } from 'react'
import {
  AppShell,
  Container,
  Group,
  ActionIcon,
  useMantineColorScheme,
  ScrollArea
} from '@mantine/core'
import { IconSun, IconMoon } from '@tabler/icons-react'
import Hero from './components/Hero'
import About from './components/About'
import Skills from './components/Skills'
import Projects from './components/Projects'
import Contact from './components/Contact'
import ParticleBackground from './components/ParticleBackground'
import Avatar from './components/Avatar'
import CyberGrid from './components/CyberGrid'
import MatrixRain from './components/MatrixRain'
import NeuralNetwork from './components/NeuralNetwork'
import CustomCursor from './components/CustomCursor'
import HUD from './components/HUD'

function App() {
  const { colorScheme, toggleColorScheme } = useMantineColorScheme()
  const [activeSection, setActiveSection] = useState('hero')
  const [isLoading, setIsLoading] = useState(true)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const appRef = useRef(null)

  // Handle mouse movement for interactive effects
  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }

    window.addEventListener('mousemove', handleMouseMove)
    return () => window.removeEventListener('mousemove', handleMouseMove)
  }, [])

  // Loading sequence
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 3000)
    return () => clearTimeout(timer)
  }, [])

  // Handle scroll to update active section
  useEffect(() => {
    const handleScroll = () => {
      const sections = ['hero', 'about', 'skills', 'projects', 'contact']
      const scrollPosition = window.scrollY + 100

      for (const section of sections) {
        const element = document.getElementById(section)
        if (element) {
          const { offsetTop, offsetHeight } = element
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveSection(section)
            break
          }
        }
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  if (isLoading) {
    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        background: 'linear-gradient(135deg, #0a0a0f 0%, #1a0a2e 50%, #16213e 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10000
      }}>
        <div className="cyber-gradient-text" style={{ fontSize: '3rem', textAlign: 'center' }}>
          <div className="glitch" data-text="INITIALIZING...">
            INITIALIZING...
          </div>
        </div>
      </div>
    )
  }

  return (
    <div ref={appRef} style={{ position: 'relative', minHeight: '100vh' }}>
      {/* Custom Cursor */}
      <CustomCursor mousePosition={mousePosition} />

      {/* Background Effects */}
      <CyberGrid />
      <MatrixRain />
      <NeuralNetwork />
      <ParticleBackground />

      {/* HUD Interface */}
      <HUD activeSection={activeSection} />

      <AppShell
        padding={0}
        style={{
          background: 'transparent',
          position: 'relative',
          zIndex: 1
        }}
      >
        {/* Futuristic Theme Toggle */}
        <ActionIcon
          onClick={() => toggleColorScheme()}
          size="xl"
          radius="xl"
          variant="filled"
          className="hud-element energy-orb"
          style={{
            position: 'fixed',
            top: 30,
            right: 30,
            zIndex: 1000,
            background: 'linear-gradient(45deg, var(--neon-cyan), var(--neon-purple))',
            border: '2px solid var(--neon-cyan)',
            boxShadow: '0 0 20px var(--neon-cyan)',
          }}
        >
          {colorScheme === 'dark' ? <IconSun size={20} /> : <IconMoon size={20} />}
        </ActionIcon>

        {/* Interactive Avatar */}
        <Avatar onSectionClick={scrollToSection} activeSection={activeSection} />

        {/* Main Content with Futuristic Layout */}
        <ScrollArea style={{ height: '100vh' }}>
          <Container size="xl" p={0} style={{ position: 'relative' }}>
            {/* Floating Islands Layout */}
            <div style={{
              position: 'relative',
              transform: 'perspective(1000px)',
              transformStyle: 'preserve-3d'
            }}>
              <div className="floating-card holographic">
                <Hero />
              </div>

              <div className="floating-card holographic" style={{
                transform: 'translateZ(50px) rotateY(2deg)',
                marginTop: '100px'
              }}>
                <About />
              </div>

              <div className="floating-card holographic" style={{
                transform: 'translateZ(100px) rotateY(-2deg)',
                marginTop: '100px'
              }}>
                <Skills />
              </div>

              <div className="floating-card holographic" style={{
                transform: 'translateZ(75px) rotateY(1deg)',
                marginTop: '100px'
              }}>
                <Projects />
              </div>

              <div className="floating-card holographic" style={{
                transform: 'translateZ(25px) rotateY(-1deg)',
                marginTop: '100px'
              }}>
                <Contact />
              </div>
            </div>
          </Container>
        </ScrollArea>
      </AppShell>
    </div>
  )
}

export default App
