"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("lottie-web"),t=require("react");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var r=n(e),o=n(t);function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function a(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){a(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.includes(n)||{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function c(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,u=[],l=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(u.push(r.value),u.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(e,t)||function(e,t){if(e){if("string"==typeof e)return i(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var f=["animationData","loop","autoplay","initialSegment","onComplete","onLoopComplete","onEnterFrame","onSegmentStart","onConfigReady","onDataReady","onDataFailed","onLoadedImages","onDOMLoaded","onDestroy","lottieRef","renderer","name","assetsPath","rendererSettings"],d=function(e,n){var i=e.animationData,a=e.loop,u=e.autoplay,d=e.initialSegment,m=e.onComplete,p=e.onLoopComplete,y=e.onEnterFrame,v=e.onSegmentStart,g=e.onConfigReady,b=e.onDataReady,S=e.onDataFailed,h=e.onLoadedImages,O=e.onDOMLoaded,w=e.onDestroy;e.lottieRef,e.renderer,e.name,e.assetsPath,e.rendererSettings;var A=s(e,f),P=c(t.useState(!1),2),j=P[0],D=P[1],E=t.useRef(),R=t.useRef(null);return t.useEffect((function(){var t=function(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(R.current){null===(t=E.current)||void 0===t||t.destroy();var o=l(l(l({},e),n),{},{container:R.current});return E.current=r.default.loadAnimation(o),D(!!E.current),function(){var e;null===(e=E.current)||void 0===e||e.destroy(),E.current=void 0}}}();return function(){return null==t?void 0:t()}}),[i,a]),t.useEffect((function(){E.current&&(E.current.autoplay=!!u)}),[u]),t.useEffect((function(){E.current&&(d?Array.isArray(d)&&d.length&&((E.current.currentRawFrame<d[0]||E.current.currentRawFrame>d[1])&&(E.current.currentRawFrame=d[0]),E.current.setSegment(d[0],d[1])):E.current.resetSegments(!0))}),[d]),t.useEffect((function(){var e=[{name:"complete",handler:m},{name:"loopComplete",handler:p},{name:"enterFrame",handler:y},{name:"segmentStart",handler:v},{name:"config_ready",handler:g},{name:"data_ready",handler:b},{name:"data_failed",handler:S},{name:"loaded_images",handler:h},{name:"DOMLoaded",handler:O},{name:"destroy",handler:w}].filter((function(e){return null!=e.handler}));if(e.length){var t=e.map((function(e){var t;return null===(t=E.current)||void 0===t||t.addEventListener(e.name,e.handler),function(){var t;null===(t=E.current)||void 0===t||t.removeEventListener(e.name,e.handler)}}));return function(){t.forEach((function(e){return e()}))}}}),[m,p,y,v,g,b,S,h,O,w]),{View:o.default.createElement("div",l({style:n,ref:R},A)),play:function(){var e;null===(e=E.current)||void 0===e||e.play()},stop:function(){var e;null===(e=E.current)||void 0===e||e.stop()},pause:function(){var e;null===(e=E.current)||void 0===e||e.pause()},setSpeed:function(e){var t;null===(t=E.current)||void 0===t||t.setSpeed(e)},goToAndStop:function(e,t){var n;null===(n=E.current)||void 0===n||n.goToAndStop(e,t)},goToAndPlay:function(e,t){var n;null===(n=E.current)||void 0===n||n.goToAndPlay(e,t)},setDirection:function(e){var t;null===(t=E.current)||void 0===t||t.setDirection(e)},playSegments:function(e,t){var n;null===(n=E.current)||void 0===n||n.playSegments(e,t)},setSubframe:function(e){var t;null===(t=E.current)||void 0===t||t.setSubframe(e)},getDuration:function(e){var t;return null===(t=E.current)||void 0===t?void 0:t.getDuration(e)},destroy:function(){var e;null===(e=E.current)||void 0===e||e.destroy(),E.current=void 0},animationContainerRef:R,animationLoaded:j,animationItem:E.current}};var m=function(e){var n=e.wrapperRef,r=e.animationItem,o=e.mode,i=e.actions;t.useEffect((function(){var e=n.current;if(e&&r&&i.length){r.stop();var t,a,u,l,s;switch(o){case"scroll":return l=null,s=function(){var t,n,o,a=(n=(t=e.getBoundingClientRect()).top,o=t.height,(window.innerHeight-n)/(window.innerHeight+o)),u=i.find((function(e){var t=e.visibility;return t&&a>=t[0]&&a<=t[1]}));if(u){if("seek"===u.type&&u.visibility&&2===u.frames.length){var s=u.frames[0]+Math.ceil((a-u.visibility[0])/(u.visibility[1]-u.visibility[0])*u.frames[1]);
//! goToAndStop must be relative to the start of the current segment
r.goToAndStop(s-r.firstFrame-1,!0)}"loop"===u.type&&(null===l||l!==u.frames||r.isPaused)&&(r.playSegments(u.frames,!0),l=u.frames),"play"===u.type&&r.isPaused&&(r.resetSegments(!0),r.play()),"stop"===u.type&&r.goToAndStop(u.frames[0]-r.firstFrame-1,!0)}},document.addEventListener("scroll",s),function(){document.removeEventListener("scroll",s)};case"cursor":return t=function(t,n){var o,a,u,l,s=t,c=n;if(-1!==s&&-1!==c){var f=(o=s,a=c,l=(u=e.getBoundingClientRect()).top,{x:(o-u.left)/u.width,y:(a-l)/u.height});s=f.x,c=f.y}var d=i.find((function(e){var t=e.position;return t&&Array.isArray(t.x)&&Array.isArray(t.y)?s>=t.x[0]&&s<=t.x[1]&&c>=t.y[0]&&c<=t.y[1]:!(!t||Number.isNaN(t.x)||Number.isNaN(t.y))&&s===t.x&&c===t.y}));if(d){if("seek"===d.type&&d.position&&Array.isArray(d.position.x)&&Array.isArray(d.position.y)&&2===d.frames.length){var m=(s-d.position.x[0])/(d.position.x[1]-d.position.x[0]),p=(c-d.position.y[0])/(d.position.y[1]-d.position.y[0]);r.playSegments(d.frames,!0),r.goToAndStop(Math.ceil((m+p)/2*(d.frames[1]-d.frames[0])),!0)}"loop"===d.type&&r.playSegments(d.frames,!0),"play"===d.type&&(r.isPaused&&r.resetSegments(!1),r.playSegments(d.frames)),"stop"===d.type&&r.goToAndStop(d.frames[0],!0)}},a=function(e){t(e.clientX,e.clientY)},u=function(){t(-1,-1)},e.addEventListener("mousemove",a),e.addEventListener("mouseout",u),function(){e.removeEventListener("mousemove",a),e.removeEventListener("mouseout",u)}}}}),[o,r])},p=function(e){var t=e.actions,n=e.mode,r=e.lottieObj,o=r.animationItem,i=r.View,a=r.animationContainerRef;return m({actions:t,animationItem:o,mode:n,wrapperRef:a}),i},y=["style","interactivity"];Object.defineProperty(exports,"LottiePlayer",{enumerable:!0,get:function(){return r.default}}),exports.default=function(e){var n,r,o,i=e.style,a=e.interactivity,u=s(e,y),l=d(u,i),c=l.View,f=l.play,m=l.stop,v=l.pause,g=l.setSpeed,b=l.goToAndStop,S=l.goToAndPlay,h=l.setDirection,O=l.playSegments,w=l.setSubframe,A=l.getDuration,P=l.destroy,j=l.animationContainerRef,D=l.animationLoaded,E=l.animationItem;return t.useEffect((function(){e.lottieRef&&(e.lottieRef.current={play:f,stop:m,pause:v,setSpeed:g,goToAndPlay:S,goToAndStop:b,setDirection:h,playSegments:O,setSubframe:w,getDuration:A,destroy:P,animationContainerRef:j,animationLoaded:D,animationItem:E})}),[null===(n=e.lottieRef)||void 0===n?void 0:n.current]),p({lottieObj:{View:c,play:f,stop:m,pause:v,setSpeed:g,goToAndStop:b,goToAndPlay:S,setDirection:h,playSegments:O,setSubframe:w,getDuration:A,destroy:P,animationContainerRef:j,animationLoaded:D,animationItem:E},actions:null!==(r=null==a?void 0:a.actions)&&void 0!==r?r:[],mode:null!==(o=null==a?void 0:a.mode)&&void 0!==o?o:"scroll"})},exports.useLottie=d,exports.useLottieInteractivity=p;
