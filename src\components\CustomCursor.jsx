import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'

const CustomCursor = ({ mousePosition }) => {
  const [isHovering, setIsHovering] = useState(false)
  const [cursorVariant, setCursorVariant] = useState('default')

  useEffect(() => {
    const handleMouseOver = (e) => {
      if (e.target.tagName === 'BUTTON' || e.target.tagName === 'A' || e.target.classList.contains('clickable')) {
        setIsHovering(true)
        setCursorVariant('hover')
      } else {
        setIsHovering(false)
        setCursorVariant('default')
      }
    }

    document.addEventListener('mouseover', handleMouseOver)
    return () => document.removeEventListener('mouseover', handleMouseOver)
  }, [])

  const variants = {
    default: {
      x: mousePosition.x - 10,
      y: mousePosition.y - 10,
      scale: 1,
      backgroundColor: 'rgba(0, 255, 255, 0.8)',
      mixBlendMode: 'difference'
    },
    hover: {
      x: mousePosition.x - 20,
      y: mousePosition.y - 20,
      scale: 2,
      backgroundColor: 'rgba(255, 0, 128, 0.8)',
      mixBlendMode: 'difference'
    }
  }

  return (
    <>
      {/* Main Cursor */}
      <motion.div
        className="custom-cursor"
        variants={variants}
        animate={cursorVariant}
        transition={{
          type: "spring",
          stiffness: 500,
          damping: 28
        }}
        style={{
          position: 'fixed',
          width: '20px',
          height: '20px',
          borderRadius: '50%',
          pointerEvents: 'none',
          zIndex: 9999,
          background: 'radial-gradient(circle, var(--neon-cyan) 0%, transparent 70%)',
          boxShadow: '0 0 20px var(--neon-cyan)'
        }}
      />

      {/* Cursor Trail */}
      <motion.div
        animate={{
          x: mousePosition.x - 25,
          y: mousePosition.y - 25,
        }}
        transition={{
          type: "spring",
          stiffness: 150,
          damping: 15,
          mass: 0.1
        }}
        style={{
          position: 'fixed',
          width: '50px',
          height: '50px',
          borderRadius: '50%',
          border: '2px solid var(--neon-cyan)',
          pointerEvents: 'none',
          zIndex: 9998,
          opacity: 0.3
        }}
      />

      {/* Outer Ring */}
      <motion.div
        animate={{
          x: mousePosition.x - 40,
          y: mousePosition.y - 40,
          rotate: 360
        }}
        transition={{
          x: { type: "spring", stiffness: 100, damping: 20 },
          y: { type: "spring", stiffness: 100, damping: 20 },
          rotate: { duration: 8, repeat: Infinity, ease: "linear" }
        }}
        style={{
          position: 'fixed',
          width: '80px',
          height: '80px',
          borderRadius: '50%',
          border: '1px solid var(--neon-purple)',
          pointerEvents: 'none',
          zIndex: 9997,
          opacity: 0.2
        }}
      />
    </>
  )
}

export default CustomCursor
