'use strict';

function _setPrototypeOf(o, p) {
  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {
    o.__proto__ = p;
    return o;
  };

  return _setPrototypeOf(o, p);
}

function _isNativeReflectConstruct() {
  if (typeof Reflect === "undefined" || !Reflect.construct) return false;
  if (Reflect.construct.sham) return false;
  if (typeof Proxy === "function") return true;

  try {
    Boolean.prototype.valueOf.call(Reflect.construct(<PERSON>olean, [], function () {}));
    return true;
  } catch (e) {
    return false;
  }
}

exports._isNativeReflectConstruct = _isNativeReflectConstruct;
exports._setPrototypeOf = _setPrototypeOf;
