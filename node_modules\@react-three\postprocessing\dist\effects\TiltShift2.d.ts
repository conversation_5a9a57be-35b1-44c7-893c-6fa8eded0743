import { BlendFunction, Effect } from 'postprocessing';
export declare class TiltShiftEffect extends Effect {
    constructor({ blendFunction, blur, // [0, 1], can go beyond 1 for extra
    taper, // [0, 1], can go beyond 1 for extra
    start, // [0,1] percentage x,y of screenspace
    end, // [0,1] percentage x,y of screenspace
    samples, // number of blur samples
    direction, }?: {
        blendFunction?: BlendFunction | undefined;
        blur?: number | undefined;
        taper?: number | undefined;
        start?: number[] | undefined;
        end?: number[] | undefined;
        samples?: number | undefined;
        direction?: number[] | undefined;
    });
}
export declare const TiltShift2: ({ blendFunction, opacity, ...props }: {
    [x: string]: any;
    blendFunction?: any;
    opacity?: any;
}) => import("react/jsx-runtime").JSX.Element;
