{"mappings": ";;;;AAAA;;ACAA;AAEA,MAAM,yDAAmC;IACvC,iBAAiB,IAAI,gBAAe;IAEpC,aAAc;QACZ,KAAK;QACL,IAAI,CAAC,YAAY,CAAC,YAAY,IAAI,uBAAsB,IAAI,aAAa;YAAC;YAAI;YAAI;YAAG;YAAI;YAAI;SAAE,GAAG;QAClG,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,uBAAsB,IAAI,aAAa;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE,GAAG;IAC1F;IAEA,wBAAwB,CAAC;AAC3B;AAEA,MAAM,kCAAY,aAAa,GAAG,IAAI;AACtC,MAAM,gCAAU,aAAa,GAAG,IAAI;AAE7B,MAAM;IACX,YAAY,QAAQ,CAAE;QACpB,IAAI,CAAC,KAAK,GAAG,IAAI,YAAW,iCAAW;QACvC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,KAAK;IAClC;IAEA,OAAO,QAAQ,EAAE;QACf,SAAS,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE;IAC9B;IAEA,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IAEA,IAAI,SAAS,KAAK,EAAE;QAClB,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;IACxB;IAEA,UAAU;QACR,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO;QAC3B,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO;IAC7B;AACF;;;ACvCA;AACA,MAAM,4CAAe;IAEjB,UAAU;QAEN,gBAAgB;YAAE,OAAO,IAAI;QAAC;QAC9B,cAAc;YAAE,OAAO,IAAI;QAAC;QAC5B,eAAe;YAAE,OAAO,IAAI;QAAC;QAC7B,WAAW;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QACxD,WAAW;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QACxD,eAAe;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QAC5D,uBAAuB;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QACpE,iBAAiB;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QAC9D,aAAa;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QAC1D,cAAc;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QAC3D,kBAAkB;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QAC/D,QAAQ;YAAE,OAAO;QAAI;QACrB,WAAW;YAAE,OAAO,EAAE;QAAC;QACvB,aAAa;YAAE,OAAO,IAAI;QAAC;QAC3B,mBAAmB;YAAE,OAAO;QAAI;QAChC,UAAU;YAAE,OAAO;QAAI;QACvB,QAAQ;YAAE,OAAO;QAAI;QACrB,OAAO;YAAE,OAAO;QAAO;QACvB,SAAS;YAAE,OAAO,KAAK;QAAC;QACxB,qBAAqB;YAAE,OAAO,KAAK;QAAC;QACpC,SAAS;YAAE,OAAO;QAAI;IAC1B;IACA,YAAY,KAAK;IACjB,WAAW,KAAK;IAChB,cAAc,QAAQ,GAAG,CAAC;;;;;CAK7B,CAAC;IAEE,gBAAgB,QAAQ,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAwO/B,CAAC;AAGF;;;AC/QA;AACA,MAAM,4CAAmB;IACrB,UAAU;QAEN,gBAAgB;YAAE,OAAO,IAAI;QAAC;QAC9B,cAAc;YAAE,OAAO,IAAI;QAAC;QAC5B,YAAY;YAAE,OAAO,IAAI;QAAC;QAC1B,uBAAuB;YAAE,OAAO,IAAI;QAAC;QACrC,sBAAsB;YAAE,OAAO,IAAI;QAAC;QACpC,2BAA2B;YAAE,OAAO,IAAI;QAAC;QACzC,qBAAqB;YAAE,OAAO,KAAK;QAAC;QACpC,WAAW;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QACxD,WAAW;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QACxD,uBAAuB;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QACpE,iBAAiB;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QAC9D,aAAa;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QAC1D,cAAc;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QAC3D,SAAS;YAAE,OAAO,aAAa,GAAG,IAAI,eAAc,GAAG,GAAG;QAAG;QAC7D,aAAa;YAAE,OAAO,IAAI;QAAC;QAC3B,oBAAoB;YAAE,OAAO,IAAI;QAAC;QAClC,QAAQ;YAAE,OAAO;QAAI;QACrB,aAAa;YAAE,OAAO;QAAK;QAC3B,cAAc;YAAE,OAAO;QAAI;QAC3B,mBAAmB;YAAE,OAAO,KAAK;QAAC;QAClC,SAAS;YAAE,OAAO,KAAK;QAAC;QACxB,QAAQ;YAAE,OAAO;QAAI;QACrB,OAAO;YAAE,OAAO;QAAO;QACvB,qBAAqB;YAAE,OAAO,KAAK;QAAC;QACpC,UAAU;YAAE,OAAO;QAAI;QACvB,mBAAmB;YAAE,OAAO;QAAI;QAChC,OAAO;YAAE,OAAO,KAAK;QAAC;QACtB,UAAU;YAAE,OAAO,KAAK;QAAC;QACzB,cAAc;YAAE,OAAO;QAAI;QAC3B,WAAW;YAAE,OAAO;QAAS;QAC7B,UAAU;YAAE,OAAO;QAAS;QAC5B,iBAAiB;YAAE,OAAO,IAAI;QAAC;QAC/B,WAAW;YAAE,OAAO;QAAI;IAE5B;IACA,YAAY,KAAK;IACjB,WAAW,KAAK;IAEhB,cAAc,QAAQ,GAAG,CAAC;;;;;GAK3B,CAAC;IACA,gBAAgB,QAAQ,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoP5B,CAAC;AAEL;;;ACtSA;AACA,MAAM,4CAAe;IACjB,UAAU;QAEN,gBAAgB;YAAE,OAAO,IAAI;QAAC;QAC9B,cAAc;YAAE,OAAO,IAAI;QAAC;QAC5B,YAAY;YAAE,OAAO,IAAI;QAAC;QAC1B,WAAW;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QACxD,WAAW;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QACxD,uBAAuB;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QACpE,iBAAiB;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QAC9D,aAAa;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QAC1D,cAAc;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QAC3D,QAAQ;YAAE,OAAO;QAAI;QACrB,KAAK;YAAE,OAAO;QAAI;QAClB,aAAa;YAAE,OAAO,IAAI;QAAC;QAC3B,UAAU;YAAE,OAAO;QAAK;QACxB,eAAe;YAAE,OAAO;QAAI;QAC5B,SAAS;YAAE,OAAO;QAAI;QACtB,eAAe;YAAE,OAAO,EAAE;QAAC;QAC3B,mBAAmB;YAAE,OAAO;QAAI;QAChC,QAAQ;YAAE,OAAO;QAAI;QACrB,OAAO;YAAE,OAAO;QAAO;QACvB,qBAAqB;YAAE,OAAO,KAAK;QAAC;IACxC;IACA,YAAY,KAAK;IACjB,WAAW,KAAK;IAEhB,cAAc,QAAQ,GAAG,CAAC;;;;;GAK3B,CAAC;IACA,gBAAgB,QAAQ,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2H5B,CAAC;AAEL;;;AC/JA;AAEA,MAAM,4CAAkB;IACpB,UAAU;QACN,cAAc;YAAE,OAAO,IAAI;QAAC;QAC5B,cAAc;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QAC3D,QAAQ;YAAE,OAAO;QAAI;QACrB,OAAO;YAAE,OAAO;QAAO;QACvB,iBAAiB;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QAC9D,uBAAuB;YAAE,OAAO,aAAa,GAAG,IAAI;QAAgB;QACpE,YAAY;YAAE,OAAO,KAAK;QAAC;QAC3B,SAAS;YAAE,OAAO,KAAK;QAAC;IAC5B;IACA,YAAY,KAAK;IACjB,WAAW,KAAK;IAEhB,cAAc,QAAQ,GAAG,CAAC;;;;;KAKzB,CAAC;IACF,gBAAgB,QAAQ,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqH3B,CAAC;AACN;;;AC5IA;;;;;;;ACAA,MAAM,kCAAY,CAAC,w1qFAAw1qF,CAAC;AAE52qF,MAAM,sCAAgC,AAAhB,aAAa,GAAI,CAAA,IAAM,WAAW,IAAI,CAAC,KAAK,kCAAY,CAAA,IAAK,EAAE,UAAU,CAAC,GAAE;IAElG,2CAAe;;;;ACJf;AACA,MAAM,gCAA0B,AAAhB,aAAa,GAAI,CAAA,IAC/B,SAAS,gBAAe,OAAO,CAAC,QAAQ,IAAG;AAItC,MAAM,4CACX,iCAAW,MACP,cAAc;IACZ,YAAY,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,CAAE;QAC1D,KAAK,CAAC,OAAO,QAAQ;YAAE,GAAG,OAAO;mBAAE;QAAM;QACzC,IAAI,CAAC,4BAA4B,GAAG,IAAI;IAC1C;IACA,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,QAAQ;IACtB;AACF,IACA,cAAc;IACZ,YAAY,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,CAAE;QAC1D,KAAK,CAAC,OAAO,QAAQ;QACrB,IAAI,CAAC,4BAA4B,GAAG,IAAI;QACxC,MAAM,UAAU,IAAI,CAAC,OAAO;QAC5B,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,QAAQ,KAAK;YAC/B,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,qBAAqB,GAAG,IAAI;QAC9C;IACF;IACA,QAAQ,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE;QAChC,IACE,IAAI,CAAC,KAAK,KAAK,SACf,IAAI,CAAC,MAAM,KAAK,UAChB,IAAI,CAAC,KAAK,KAAK,OACf;YACA,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,KAAK,GAAG;YACb,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,IAAI,IAAK;gBACrD,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG;gBAC9B,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG;gBAC/B,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG;YAChC;YACA,IAAI,CAAC,OAAO;QACd,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,OAAO;QAC/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,OAAO;IAChC;IACA,KAAK,MAAM,EAAE;QACX,IAAI,CAAC,OAAO;QACZ,IAAI,CAAC,KAAK,GAAG,OAAO,KAAK;QACzB,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM;QAC3B,IAAI,CAAC,KAAK,GAAG,OAAO,KAAK;QACzB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,OAAO;QAChC,IAAI,CAAC,WAAW,GAAG,OAAO,WAAW;QACrC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,QAAQ;QAClC,IAAI,CAAC,WAAW,GAAG,OAAO,WAAW;QACrC,IAAI,CAAC,aAAa,GAAG,OAAO,aAAa;QACzC,IAAI,OAAO,YAAY,KAAK,IAAI,EAC9B,IAAI,CAAC,YAAY,GAAG,OAAO,YAAY,CAAC,KAAK;QAC/C,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG;QACtB,IAAK,IAAI,IAAI,GAAG,KAAK,OAAO,OAAO,CAAC,MAAM,EAAE,IAAI,IAAI,IAAK;YACvD,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK;YACzC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,qBAAqB,GAAG,IAAI;QAC9C;QACA,OAAO,IAAI;IACb;AACF,CAAC;;;AFvDP;;;;;CAKC,GACD,SAAS,sCAAgB,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE;IAC3C,MAAM,YAAY,GAAG,iBAAiB,CAAC,YAAY,GAAG,sBAAsB;IAC5E,IAAI,WAAW;QACX,MAAM,kBAAkB,GAAG,iBAAiB,CAAC,YAAY,GAAG,YAAY;QACxE,MAAM,kBAAkB,kBAAkB;QAC1C,KAAK,QAAQ,GAAG,KAAK,QAAQ,KAAK,IAAI,kBAAkB,KAAK,kBAAkB,GAAG,KAAK,QAAQ,GAAG,AAAC,CAAA,IAAI,KAAK,kBAAkB,AAAD,IAAK,eAAe;IACrJ,OACI,gEAAgE;IAChE,WAAW,IAAM;QACb,sCAAgB,YAAY,IAAI;IACpC,GAAG;AAEX;AACA,MAAM,kDAAqB,CAAA,GAAA,YAAI,AAAD;IAC1B;;;;;;;;;;;KAWC,GACD,YAAY,KAAK,EAAE,MAAM,EAAE,QAAQ,GAAG,EAAE,SAAS,GAAG,CAAE;QAClD,KAAK;QACL,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QAEd,IAAI,CAAC,KAAK,GAAG,IAAI;QAEjB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG;QACb;;;;;;;;;;;;;;;;;;SAkBC,GACD,IAAI,CAAC,YAAY,GAAG,IAAI;QACxB,IAAI,CAAC,aAAa,GAAG,IAAI,MAAM;YAC3B,WAAW;YACX,UAAU;YACV,SAAS;YACT,gBAAgB;YAChB,eAAe;YACf,iBAAiB;YACjB,WAAW;YACX,mBAAmB;YACnB,YAAY;YACZ,YAAY;YACZ,gBAAgB;YAChB,OAAO,IAAI,aAAY,GAAG,GAAG;YAC7B,iBAAiB,IAAI;YACrB,iBAAiB,CAAA,GAAA,yCAAS,AAAD,EAAE,OAAO;YAClC,mBAAmB,KAAK;YACxB,SAAS,KAAK;YACd,sBAAsB,IAAI;YAC1B,eAAe,IAAI;YACnB,mBAAmB,KAAK;YACxB,YAAY,KAAK;QACrB,GAAG;YACC,KAAK,CAAC,QAAQ,UAAU,QAAU;gBAC9B,MAAM,UAAU,MAAM,CAAC,SAAS;gBAChC,MAAM,CAAC,SAAS,GAAG;gBACnB,IAAI,MAAM,MAAM,EACZ;oBAAA,IAAI,CAAC,MAAM,MAAM,CAAC,UACd,IAAI,CAAC,UAAU;gBACnB,OAEA,IAAI,YAAY,OACZ,IAAI,CAAC,UAAU;gBAGvB,IAAI,aAAa,eAAe,YAAY,OACxC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;gBAE7F,IAAI,aAAa,oBAAoB,YAAY,OAC7C,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;gBAElG,IAAI,aAAa,aAAa,YAAY,OAAO;oBAC7C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;oBACzF,IAAI,CAAC,uBAAuB;oBAC5B,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;oBACnG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM;gBACxC,CAAC;gBACD,IAAI,aAAa,0BAA0B,YAAY,OACnD,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;gBAEvG,IAAI,aAAa,mBACb,IAAI,CAAC,YAAY,GAAG,KAAK;gBAE7B,IAAI,aAAa,uBAAuB,YAAY,OAAO;oBACvD,IAAI,CAAC,sBAAsB,GAAG,KAAK;oBACnC,IAAI,CAAC,2BAA2B;gBACpC,CAAC;gBACD,OAAO,IAAI;YACf;QACJ;QACA,4BAA4B,GAC5B,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,4BAA4B,GAC5B,IAAI,CAAC,cAAc,GAAG,EAAE;QACxB,IAAI,CAAC,sBAAsB,GAAG,IAAI;QAClC,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,cAAc,GAAG,IAAI;QAC1B,IAAI,CAAC,oBAAoB,GAAG,IAAI;QAChC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe;QACjE,IAAI,CAAC,8BAA8B;QACnC,IAAI,CAAC,uBAAuB;QAC5B,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,2BAA2B;QAEhC,mGAAmG;QACnG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA,GAAA,yCAAkB,AAAD,EAAE,IAAI,sBAAqB;YAC5D,UAAU;gBACN,UAAU;oBACN,OAAO,IAAI;gBACf;YACJ;YACA,YAAY,KAAK;YACjB,cAAc,CAAC;;;;;;YAMf,CAAC;YACD,gBAAgB,CAAC;;;;;;YAMjB,CAAC;QACL;QACA,IAAI,CAAC,mBAAmB,GAAG,IAAI,yBAAwB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;YAC5E,WAAW;YACX,WAAW;YACX,aAAa,KAAK;YAClB,QAAQ;QACZ;QACA,IAAI,CAAC,kBAAkB,GAAG,IAAI,yBAAwB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;YAC3E,WAAW;YACX,WAAW;YACX,aAAa,KAAK;YAClB,QAAQ;QACZ;QACA,IAAI,CAAC,oBAAoB,GAAG,IAAI,yBAAwB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;YAC7E,WAAW;YACX,WAAW;YACX,aAAa,KAAK;QACtB;QACA,IAAI,CAAC,wBAAwB,GAAG,IAAI,yBAAwB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;YACjF,WAAW;YACX,WAAW;YACX,aAAa,KAAK;YAClB,QAAQ;YACR,MAAM;YACN,eAAe,KAAK;YACpB,aAAa,KAAK;YAClB,OAAO,IAAI;QACf;QACA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAA,GAAA,yCAAkB,AAAD,EAAE,IAAI,sBAAqB;YACpE,UAAU;gBACN,OAAO;oBAAE,OAAO;gBAAE;gBAClB,UAAU;oBAAE,OAAO,IAAI;gBAAC;YAC5B;YACA,aAAa,IAAI;YACjB,SAAS;YACT,cAAc,CAAC;;;;;cAKb,CAAC;YACH,gBAAgB,CAAC;;;;;;;;gBAQb,CAAC;QACT;QAGA,8BAA8B,GAC9B,IAAI,CAAC,SAAS,GACV,IAAI,mBACA,CAAA,GAAA,wCAAa,AAAD,GACZ,KACA;QAER,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG;QAC5B,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG;QACvB,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG;QACvB,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG;QAC3B,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG;QAC3B,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI;QACjC,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAAC,SAAS,GAAG,IAAI;QACrB,IAAI,CAAC,EAAE,GAAG,IAAI;QACd,IAAI,CAAC,EAAE,GAAG,IAAI;IAIlB;IACA,0BAA0B;QACtB,IAAI,CAAC,UAAU;QAEf,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;YAC5B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAA,GAAA,yCAAgC,AAAD,EAC5D,IAAI,CAAC,KAAK,GAAG,GACb,IAAI,CAAC,MAAM,GAAG,GACd;YAGJ,IAAI,mBAAkB,KAClB,IAAI,CAAC,qBAAqB,CAAC,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO;YAG5E,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,GAAG;YAChD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,GAAG;YAC9C,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,GAAG;YACnD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,GAAG;YACnD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,WAAW,GAAG,KAAK;YAC1D,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,GAAG;YAChD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,GAAG;YAC9C,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,GAAG;YACnD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,GAAG;YACnD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,WAAW,GAAG,KAAK;YAG1D,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA,GAAA,yCAAkB,AAAD,EAAE,IAAI,sBAAqB,CAAA,GAAA,yCAAe,AAAD;QAC7F,OAAO;YACH,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBAClC,IAAI,CAAC,qBAAqB,GAAG,IAAI;YACrC,CAAC;YACD,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,CAAC,mBAAmB,CAAC,OAAO;gBAChC,IAAI,CAAC,mBAAmB,GAAG,IAAI;YACnC,CAAC;QACL,CAAC;IACL;IACA,qBAAqB;QACjB,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,iBAAiB,KAAK;YAC1B,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAQ;gBACzB,IAAI,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,WAAW,EACxC,iBAAiB,IAAI;YAE7B;YACA,IAAI,gBACA,IAAI,CAAC,aAAa,CAAC,iBAAiB,GAAG,IAAI;QAEnD,CAAC;IACL;IACA,8BAA8B;QAC1B,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE;YACtC,IAAI,CAAC,+BAA+B,GAAG,IAAI,yBAAwB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;gBACxF,WAAW;gBACX,WAAW;gBACX,MAAM;gBACN,QAAQ;YACZ;YACA,IAAI,CAAC,8BAA8B,GAAG,IAAI,yBAAwB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;gBACvF,WAAW;gBACX,WAAW;gBACX,MAAM;gBACN,QAAQ;YACZ;YACA,IAAI,CAAC,8BAA8B,CAAC,YAAY,GAAG,IAAI,oBAAmB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;YACnG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA,GAAA,yCAAkB,AAAD,EAAE,IAAI,sBAAqB;gBACjE,UAAU;oBACN,cAAc;wBAAE,OAAO,IAAI,CAAC,YAAY;oBAAC;oBACzC,oBAAoB;wBAAE,OAAO,IAAI,CAAC,aAAa,CAAC,eAAe,KAAK,CAAA,GAAA,yCAAQ,EAAE,OAAO;oBAAC;gBAC1F;gBACA,cAAc,QAAQ,GAAG,CAAC;;;;;aAK7B,CAAC;gBACE,gBAAgB,QAAQ,GAAG,CAAC;;;;;;;;;;;;;;;;;YAiBhC,CAAC;YAED;QACJ,OAAO;YACH,IAAI,IAAI,CAAC,+BAA+B,EAAE;gBACtC,IAAI,CAAC,+BAA+B,CAAC,OAAO;gBAC5C,IAAI,CAAC,+BAA+B,GAAG,IAAI;YAC/C,CAAC;YACD,IAAI,IAAI,CAAC,8BAA8B,EAAE;gBACrC,IAAI,CAAC,8BAA8B,CAAC,OAAO;gBAC3C,IAAI,CAAC,8BAA8B,GAAG,IAAI;YAC9C,CAAC;YACD,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,aAAa,CAAC,OAAO;gBAC1B,IAAI,CAAC,aAAa,GAAG,IAAI;YAC7B,CAAC;QACL,CAAC;IACL;IACA,mBAAmB,QAAQ,EAAE;QACzB,MAAM,gBAAgB,IAAI,CAAC,KAAK,CAAC,UAAU;QAC3C,MAAM,gBAAgB,SAAS,aAAa,CAAC,IAAI;QACjD,MAAM,gBAAgB,SAAS,aAAa;QAC5C,MAAM,gBAAgB,IAAI;QAC1B,MAAM,oBAAoB,SAAS,cAAc;QACjD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAQ;YACzB,cAAc,GAAG,CAAC,KAAK,IAAI,OAAO;QACtC;QAEA,qBAAqB;QACrB,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI;QAC5B,SAAS,cAAc,GAAG,KAAK;QAC/B,SAAS,aAAa,CAAC,IAAI,aAAY,GAAG,GAAG,IAAI;QAEjD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY;QAC3E,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,KAAK,CAAA,GAAA,yCAAQ,EAAE,OAAO;QACxH,qDAAqD;QACrD,SAAS,eAAe,CAAC,IAAI,CAAC,+BAA+B;QAC7D,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAQ;YACzB,IAAI,IAAI,QAAQ,EACZ,IAAI,OAAO,GAAG,cAAc,GAAG,CAAC,QAAS,CAAA,AAAC,IAAI,QAAQ,CAAC,WAAW,IAAI,CAAC,IAAI,QAAQ,CAAC,UAAU,IAAI,CAAC,IAAI,QAAQ,CAAC,aAAa,IAAK,CAAC,CAAC,IAAI,QAAQ,CAAC,eAAe,AAAD;QAEvK;QACA,SAAS,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;QAC/B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC1B,SAAS,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM;QAEvC,kDAAkD;QAElD,SAAS,eAAe,CAAC,IAAI,CAAC,8BAA8B;QAC5D,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAQ;YACzB,IAAI,IAAI,QAAQ,EACZ,IAAI,OAAO,GAAG,cAAc,GAAG,CAAC,QAAQ,IAAI,QAAQ,CAAC,WAAW,IAAI,IAAI,QAAQ,CAAC,UAAU,IAAI,CAAC,IAAI,QAAQ,CAAC,aAAa;QAElI;QACA,SAAS,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;QAC/B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC1B,SAAS,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM;QAEvC,UAAU;QACV,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAQ;YACzB,IAAI,OAAO,GAAG,cAAc,GAAG,CAAC;QACpC;QACA,SAAS,aAAa,CAAC,eAAe;QACtC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG;QACxB,SAAS,cAAc,GAAG;IAC9B;IACA,iCAAiC;QAC7B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;QACzF,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;IAClG;IACA,gBAAgB,kBAAkB,CAAA,GAAA,yCAAQ,EAAE,OAAO,EAAE,QAAQ,KAAK,EAAE;QAChE,IAAI,CAAC,UAAU;QACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS;QAC1E,MAAM,IAAI;YAAC,GAAG,CAAA,GAAA,yCAAW,CAAC;QAAC;QAC3B,EAAE,cAAc,GAAG,EAAE,cAAc,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG;QAC/H,IAAI,oBAAoB,CAAA,GAAA,yCAAQ,EAAE,GAAG,EACjC,EAAE,cAAc,GAAG,uBAAuB,EAAE,cAAc;aACvD,IAAI,oBAAoB,CAAA,GAAA,yCAAQ,EAAE,OAAO,EAC5C,EAAE,cAAc,GAAG,2BAA2B,EAAE,cAAc;QAElE,IAAI,OACA,EAAE,cAAc,GAAG,oBAAoB,EAAE,cAAc;QAE3D,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAC1B,EAAE,cAAc,GAAG,sBAAsB,EAAE,cAAc;QAE7D,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO;YACtC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,IAAI,sBAAqB;QAC9D,OACI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAA,GAAA,yCAAkB,AAAD,EAAE,IAAI,sBAAqB;IAEhF;IACA,qBAAqB,kBAAkB,CAAA,GAAA,yCAAQ,EAAE,OAAO,EAAE,QAAQ,KAAK,EAAE;QACrE,IAAI,CAAC,UAAU;QACf,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE;QACrF,MAAM,IAAI;YAAC,GAAG,CAAA,GAAA,yCAAW,CAAC;QAAC;QAC3B,EAAE,cAAc,GAAG,EAAE,cAAc,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc;QACnF,IAAI,oBAAoB,CAAA,GAAA,yCAAQ,EAAE,GAAG,EACjC,EAAE,cAAc,GAAG,uBAAuB,EAAE,cAAc;aACvD,IAAI,oBAAoB,CAAA,GAAA,yCAAQ,EAAE,OAAO,EAC5C,EAAE,cAAc,GAAG,2BAA2B,EAAE,cAAc;QAElE,IAAI,OACA,EAAE,cAAc,GAAG,oBAAoB,EAAE,cAAc;QAE3D,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO;YACrC,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,IAAI,sBAAqB;QAC7D,OACI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAA,GAAA,yCAAkB,AAAD,EAAE,IAAI,sBAAqB;IAE/E;IACA,0BAA0B,kBAAkB,CAAA,GAAA,yCAAQ,EAAE,OAAO,EAAE,QAAQ,KAAK,EAAE;QACtE,IAAI,CAAC,UAAU;QAEf,MAAM,IAAI;YAAC,GAAG,CAAA,GAAA,yCAAe,CAAC;QAAC;QAC/B,IAAI,oBAAoB,CAAA,GAAA,yCAAQ,EAAE,GAAG,EACjC,EAAE,cAAc,GAAG,uBAAuB,EAAE,cAAc;aACvD,IAAI,oBAAoB,CAAA,GAAA,yCAAQ,EAAE,OAAO,EAC5C,EAAE,cAAc,GAAG,2BAA2B,EAAE,cAAc;QAElE,IAAI,OACA,EAAE,cAAc,GAAG,oBAAoB,EAAE,cAAc;QAE3D,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,oBAAoB,EACrE,EAAE,cAAc,GAAG,sBAAsB,EAAE,cAAc;QAE7D,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,OAAO;YAC1C,IAAI,CAAC,oBAAoB,CAAC,QAAQ,GAAG,IAAI,sBAAqB;QAClE,OACI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAA,GAAA,yCAAkB,AAAD,EAAE,IAAI,sBAAqB;IAEpF;IACA;;;;SAIC,GACL,0BAA0B,CAAC,EAAE;QACrB,MAAM,SAAS,EAAE;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YACxB,MAAM,QAAQ,WAAW;YACzB,MAAM,IAAK,KAAK,IAAI,CAAC,IAAI,OAAO,KAAK,IAAI,CAAC;YAC1C,MAAM,IAAI,IAAI,KAAK,GAAG,CAAC;YACvB,MAAM,IAAI,IAAI,KAAK,GAAG,CAAC;YACvB,wBAAwB;YACxB,MAAM,IAAI,KAAK,IAAI,CAAC,IAAK,CAAA,IAAI,IAAI,IAAI,CAAA;YACrC,OAAO,IAAI,CAAC,IAAI,eAAc,GAAG,GAAG;QAExC;QACA,OAAO;IACX;IACA;;;;;SAKC,GACL,uBAAuB,UAAU,EAAE,QAAQ,EAAE;QACzC,MAAM,YAAY,IAAI,KAAK,EAAE,GAAG,WAAW;QAC3C,MAAM,gBAAgB,MAAM;QAC5B,MAAM,aAAa;QACnB,MAAM,UAAU,EAAE;QAClB,IAAI,SAAS;QACb,IAAI,QAAQ;QACZ,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;YACjC,QAAQ,IAAI,CAAC,IAAI,eAAc,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,cAAc,CAAC,KAAK,GAAG,CAAC,QAAQ;YACjG,UAAU;YACV,SAAS;QACb;QACA,OAAO;IACX;IACA,QAAQ,KAAK,EAAE,MAAM,EAAE;QACnB,IAAI,CAAC,UAAU;QACf,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,MAAM,CAAC;QAC9C,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAC7B,GAAG,SACH;QACJ,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,QAC5B,GAAG,SACH;QACJ,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,QAAQ,GAAG,SAAS;QAC1D,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAC1B,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,QAAQ,GAAG,SAAS;QAE3D,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE;YACtC,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,OAAO;YACpD,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,OAAO;QACvD,CAAC;QACD,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,OAAO;IAC7C;IACA,gBAAgB,YAAY,EAAE;QAC1B,IAAI,CAAC,YAAY,GAAG;IACxB;IACA,aAAa;QACT,IAAI,CAAC,UAAU,GAAG,IAAI;IAC1B;IACA,OAAO,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE;QACpC,MAAM,YAAY,SAAS,EAAE,CAAC,OAAO;QACrC,SAAS,EAAE,CAAC,OAAO,GAAG,KAAK;QAE3B,mCAAmC;QACnC,yCAAyC;QACzC,yEAAyE;QACzE,oCAAoC;QAEpC,IAAI,SAAS,YAAY,CAAC,sBAAsB,IAAI,IAAI,CAAC,aAAa,CAAC,eAAe,KAAK,CAAA,GAAA,yCAAQ,EAAE,GAAG,IAAI,SAAS,YAAY,CAAC,kBAAkB,IAAI,IAAI,CAAC,aAAa,CAAC,eAAe,KAAK,CAAA,GAAA,yCAAQ,EAAE,OAAO,EAAE;YAC9M,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,SAAS,YAAY,CAAC,sBAAsB,GAAG,CAAA,GAAA,yCAAS,AAAD,EAAE,GAAG,GAAG,SAAS,YAAY,CAAC,kBAAkB,GAAG,CAAA,GAAA,yCAAS,AAAD,EAAE,OAAO,GAAG,CAAA,GAAA,yCAAS,AAAD,EAAE,OAAO;YACpL,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;YACzF,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;YAC9F,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;QACvG,CAAC;QACD,IAAI,CAAC,kBAAkB;QACvB,IAAI,YAAY,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,IACnE,YAAY,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,EACzE;YACE,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,GAAG,YAAY,OAAO,CAAC,IAAI;YACjE,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,GAAG,YAAY,OAAO,CAAC,MAAM;YACrE,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI;QACxD,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,iBAAiB;QAC7B,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,KAAK,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,KAAK,IAAI,CAAC,aAAa,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,EACjL,IAAI,CAAC,KAAK;aACP;YACH,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE;gBAC/B,SAAS,eAAe,CAAC,IAAI,CAAC,wBAAwB;gBACtD,SAAS,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;YACnC,CAAC;YACD,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,UAAU,GAAG,KAAK;QAC3B,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB;QACvD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB;QAC3D,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,KAAK,SAAS,UAAU;YACxB,MAAM,GAAG,YAAY,CAAC;YACtB,IAAI,QAAQ,IAAI,EAAE;gBACd,QAAQ,KAAK,CAAC;gBACd,IAAI,CAAC,SAAS,GAAG,KAAK;YAC1B,CAAC;QACL,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,aAAa,GAAG,WAAW;YAC3B,GAAG,UAAU,CAAC,IAAI,gBAAgB,EAAE;QACxC,CAAC;QACD,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,EACpC,IAAI,CAAC,kBAAkB,CAAC;QAE5B,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM;QACnC,IAAI,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;QAC5C,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAClE,cAAc;QAElB,IAAI,IAAI,CAAC,KAAK,GAAG,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;YAClD,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;gBAE5B,SAAS,eAAe,CAAC,IAAI,CAAC,qBAAqB;gBACnD,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY;gBAC/E,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE;gBACrE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;gBAC3E,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG;gBACzE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB;gBAC7G,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW;gBAC3F,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB;gBACxG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB;gBAC5F,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YACpC,CAAC;YACD,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,KAAK,GAAG,YAAY,OAAO;YACnF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY;YACrJ,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI;YACzI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB;YACvF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB;YACzF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK;YACjJ,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB;YAC1G,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW;YACxF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI;YAC9F,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,iBAAiB,CAAC,KAAK,GAAG,IAAI,eAAc,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc;YACpJ,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAI,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG,cAAc,CAAC,KAAO,KAAK,KAAK,IAAI,CAAC,EAAE;YACnJ,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,GAAG,YAAY,GAAG,KAAK;YAC5E,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO;YACvE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS;YAC3E,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG;YAC1D,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe;YACrG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;YACxE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG;YACtE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB;YACzF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB;YACzG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;YACnE,eAAe;YACf,SAAS,eAAe,CAAC,IAAI,CAAC,mBAAmB;YACjD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;YAC7B,aAAa;YACb,iBAAiB;YACjB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,IAAK;gBAC3D,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,kBAAkB,CAAC,GAAG;oBAAC,IAAI,CAAC,kBAAkB;oBAAE,IAAI,CAAC,mBAAmB;iBAAC;gBACzG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO;gBAC1F,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY;gBACpJ,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB;gBACtF,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB;gBACxF,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB;gBACzG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvF,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI;gBAC7F,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAI,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG,cAAc,CAAC,KAAO,KAAK,KAAK,IAAI,CAAC,EAAE;gBAClJ,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,GAAG,YAAY,GAAG,KAAK;gBAC3E,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS;gBAC1E,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,GACrF,CAAA,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,MAAQ,CAAC,AAAD;gBAEzC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,GAAG;gBAC9D,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe;gBACpG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG;gBACxD,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc;gBACjF,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;gBACvE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG;gBACrE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBACxG,SAAS,eAAe,CAAC,IAAI,CAAC,mBAAmB;gBACjD,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;YAEhC;YACA,SAAS,eAAe,CAAC,IAAI,CAAC,wBAAwB;YACtD,MAAM,eAAe,SAAS,SAAS;YACvC,SAAS,SAAS,GAAG,KAAK;YAC1B,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO;YAC5F,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;YACnE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;YAC7B,SAAS,SAAS,GAAG;QACzB,CAAC;QACD,qDAAqD;QACrD,eAAe;QACf,wBAAwB;QACxB,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE;YACtC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC,KAAK,GAAG,IAAI,CAAC,+BAA+B,CAAC,OAAO;YACvH,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,qBAAqB,CAAC,KAAK,GAAG,IAAI,CAAC,8BAA8B,CAAC,OAAO;YACrH,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,0BAA0B,CAAC,KAAK,GAAG,IAAI,CAAC,8BAA8B,CAAC,YAAY;YAC/H,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,KAAK,GAAG,IAAI;QACjF,CAAC;QACD,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,KAAK,GAAG,YAAY,OAAO;QACvF,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY;QACnF,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO;QACzF,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;QAC5E,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG;QAC1E,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB;QAC9G,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW;QAC5F,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB;QAC7F,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY;QAC/J,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE;QACzE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS;QAC/E,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS;QAC7F,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU;QAC/F,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB;QAC7G,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG;QAC9D,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe;QACzG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,GACpF,IAAI,CAAC,cAAc,GACnB,IAAI,CAAC,aAAa,CAAC,eAAe;QACtC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO;QACrG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GACtD,IAAI,CAAC,EAAE,CAAC,IAAI,CACR,IAAI,CAAC,aAAa,CAAC,KAAK,EAC1B,mBAAmB;QACzB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa;QACrG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI;QAClG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG;QAC3E,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;YAChB,IACI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EACtB;gBACE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,KAAK;gBACnE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI;gBAClF,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG;YACpF,OAAO,IACH,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,EAC1B;gBACE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI;gBAClE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO;YAC5F,OACI,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC;QAI5F,CAAC;QACD,SAAS,eAAe,CACpB;6BACa,GACb,IAAI,CAAC,oBAAoB;QAE7B,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;QACjC,SAAS,eAAe,CACpB,IAAI,CAAC,cAAc,GAAG,IAAI,GAC1B,YAAY;QAEhB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO;QACrF,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACrB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,GAAG,QAAQ,CAAC,IAAI,gBAAgB;YAChC,sCAAgB,YAAY,IAAI,IAAI;QACxC,CAAC;QAED,SAAS,EAAE,CAAC,OAAO,GAAG;IAC1B;IACA;;SAEC,GACL,kBAAkB;QACV,IAAI,CAAC,SAAS,GAAG,IAAI;IACzB;IACA;;SAEC,GACL,mBAAmB;QACX,IAAI,CAAC,SAAS,GAAG,KAAK;IAC1B;IACA;;;SAGC,GACL,eAAe,IAAI,EAAE;QACb,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG;YAAC;YAAY;YAAM;YAAS;YAAS;SAAW,CAAC,OAAO,CAAC;IAC7F;IACA;;;SAGC,GACL,eAAe,IAAI,EAAE;QACjB,IAAI,SAAS,eAAe;YACxB,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG;YAC/B,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG;YACpC,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG;QACvC,OAAO,IAAI,SAAS,OAAO;YACvB,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG;YAC/B,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG;YACpC,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG;QACvC,OAAO,IAAI,SAAS,UAAU;YAC1B,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG;YAC/B,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG;YACpC,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG;QACvC,OAAO,IAAI,SAAS,QAAQ;YACxB,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG;YAC/B,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG;YACpC,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG;QACvC,OAAO,IAAI,SAAS,SAAS;YACzB,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG;YAC/B,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG;YACpC,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG;QACvC,CAAC;IAEL;AACJ;;;;;AN5wBA;;;;;CAKC,GACD,SAAS,sCAAgB,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE;IAC3C,MAAM,YAAY,GAAG,iBAAiB,CAAC,YAAY,GAAG,sBAAsB;IAC5E,IAAI,WAAW;QACX,MAAM,kBAAkB,GAAG,iBAAiB,CAAC,YAAY,GAAG,YAAY;QACxE,MAAM,kBAAkB,kBAAkB;QAC1C,KAAK,QAAQ,GAAG,KAAK,QAAQ,KAAK,IAAI,kBAAkB,KAAK,kBAAkB,GAAG,KAAK,QAAQ,GAAG,AAAC,CAAA,IAAI,KAAK,kBAAkB,AAAD,IAAK,eAAe;IACrJ,OACI,gEAAgE;IAChE,WAAW,IAAM;QACb,sCAAgB,YAAY,IAAI;IACpC,GAAG;AAEX;AAEO,MAAM,4CAAY;IACrB,SAAS;IACT,KAAK;IACL,SAAS;AACb;AAEA,MAAM,kDAAiB,CAAA,GAAA,WAAI,AAAD;IACtB;;;;;;;;;;;KAWC,GACD,YAAY,KAAK,EAAE,MAAM,EAAE,QAAQ,GAAG,EAAE,SAAS,GAAG,CAAE;QAClD,KAAK;QACL,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QAEd,IAAI,CAAC,KAAK,GAAG,IAAI;QAEjB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG;QACb;;;;;;;;;;;;;;;;;;;SAmBC,GACD,IAAI,CAAC,aAAa,GAAG,IAAI,MAAM;YAC3B,WAAW;YACX,UAAU;YACV,SAAS;YACT,gBAAgB;YAChB,eAAe;YACf,iBAAiB;YACjB,WAAW;YACX,mBAAmB;YACnB,YAAY;YACZ,YAAY;YACZ,gBAAgB;YAChB,OAAO,IAAI,aAAY,GAAG,GAAG;YAC7B,iBAAiB,IAAI;YACrB,iBAAiB,0CAAU,OAAO;YAClC,mBAAmB,KAAK;YACxB,SAAS,KAAK;YACd,sBAAsB,IAAI;YAC1B,kBAAkB,IAAI;YACtB,eAAe,IAAI;YACnB,mBAAmB,KAAK;YACxB,SAAS,KAAK;YACd,YAAY,KAAK;QACrB,GAAG;YACC,KAAK,CAAC,QAAQ,UAAU,QAAU;gBAC9B,MAAM,UAAU,MAAM,CAAC,SAAS;gBAChC,MAAM,CAAC,SAAS,GAAG;gBACnB,IAAI,MAAM,MAAM,EACZ;oBAAA,IAAI,CAAC,MAAM,MAAM,CAAC,UACd,IAAI,CAAC,UAAU;gBACnB,OAEA,IAAI,YAAY,OACZ,IAAI,CAAC,UAAU;gBAGvB,IAAI,aAAa,eAAe,YAAY,OACxC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;gBAE7F,IAAI,aAAa,oBAAoB,YAAY,OAC7C,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;gBAElG,IAAI,aAAa,aAAa,YAAY,OAAO;oBAC7C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;oBACzF,IAAI,CAAC,uBAAuB;oBAC5B,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;oBACnG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM;gBACxC,CAAC;gBACD,IAAI,aAAa,0BAA0B,YAAY,OACnD,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;gBAEvG,IAAI,aAAa,uBAAuB,YAAY,OAAO;oBACvD,IAAI,CAAC,sBAAsB,GAAG,KAAK;oBACnC,IAAI,CAAC,2BAA2B;gBACpC,CAAC;gBACD,IAAI,aAAa,aAAa,YAAY,OAAO;oBAC7C;;;;iEAI6C,GAC7C,IAAI,CAAC,kBAAkB,CAAC,OAAO;oBAC/B,IAAI,CAAC,kBAAkB,GAAG,IAAI,yBAAwB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;wBAC3E,WAAW;wBACX,WAAW;wBACX,MAAM;wBACN,QAAQ;wBACR,eAAe;oBACnB;oBACA,IAAI,CAAC,kBAAkB,CAAC,YAAY,GAAG,IAAI,oBAAmB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,4BAA2B,sBAAqB;oBAC/I,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,MAAM,GAAG,QAAQ,4BAA2B,kBAAiB;gBACtG,CAAC;gBACD,OAAO,IAAI;YACf;QACJ;QACA,4BAA4B,GAC5B,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,4BAA4B,GAC5B,IAAI,CAAC,cAAc,GAAG,EAAE;QACxB,IAAI,CAAC,sBAAsB,GAAG,IAAI;QAClC,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,cAAc,GAAG,IAAI;QAC1B,IAAI,CAAC,oBAAoB,GAAG,IAAI;QAChC,IAAI,CAAC,kBAAkB,GAAG,IAAI,yBAAwB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;YAC3E,WAAW;YACX,WAAW;YACX,MAAM;YACN,QAAQ;YACR,eAAe,KAAK;QACxB;QACA,IAAI,CAAC,kBAAkB,CAAC,YAAY,GAAG,IAAI,oBAAmB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;QACvF,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,MAAM,GAAG;QAC9C,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;QACnG,IAAI,CAAC,8BAA8B;QACnC,IAAI,CAAC,uBAAuB;QAC5B,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,2BAA2B;QAGhC,IAAI,CAAC,mBAAmB,GAAG,IAAI,yBAAwB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;YAC5E,WAAW;YACX,WAAW;YACX,aAAa,KAAK;YAClB,QAAQ;QACZ;QACA,IAAI,CAAC,kBAAkB,GAAG,IAAI,yBAAwB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;YAC3E,WAAW;YACX,WAAW;YACX,aAAa,KAAK;YAClB,QAAQ;QACZ;QACA,IAAI,CAAC,wBAAwB,GAAG,IAAI,yBAAwB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;YACjF,WAAW;YACX,WAAW;YACX,aAAa,KAAK;YAClB,QAAQ;YACR,MAAM;YACN,eAAe,KAAK;YACpB,aAAa,KAAK;YAClB,OAAO,IAAI;QACf;QAGA,8BAA8B,GAC9B,IAAI,CAAC,SAAS,GACV,IAAI,mBACA,CAAA,GAAA,wCAAa,AAAD,GACZ,KACA;QAER,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAA,GAAA,yCAAkB,AAAD,EAAE,IAAI,sBAAqB;YACpE,UAAU;gBACN,OAAO;oBAAE,OAAO;gBAAE;gBAClB,UAAU;oBAAE,OAAO,IAAI;gBAAC;YAC5B;YACA,aAAa,IAAI;YACjB,SAAS;YACT,cAAc,CAAC;;;;;cAKb,CAAC;YACH,gBAAgB,CAAC;;;;;;;;gBAQb,CAAC;QACT;QACA,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG;QAC5B,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG;QACvB,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG;QACvB,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG;QAC3B,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG;QAC3B,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI;QACjC,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,EAAE,GAAG,IAAI;QACd,IAAI,CAAC,EAAE,GAAG,IAAI;IAElB;IACA,0BAA0B;QACtB,IAAI,CAAC,UAAU;QACf,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;YAC5B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAA,GAAA,yCAAgC,AAAD,EAC5D,IAAI,CAAC,KAAK,GAAG,GACb,IAAI,CAAC,MAAM,GAAG,GACd;YAGJ,IAAI,mBAAkB,KAClB,IAAI,CAAC,qBAAqB,CAAC,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO;YAE5E,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,GAAG;YAChD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,GAAG;YAC9C,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,GAAG;YACnD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,GAAG;YACnD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,WAAW,GAAG,KAAK;YAC1D,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,GAAG;YAChD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,GAAG;YAC9C,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,GAAG;YACnD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,GAAG;YACnD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,WAAW,GAAG,KAAK;YAE1D,MAAM,IAAI;gBAAC,GAAG,CAAA,GAAA,yCAAc,CAAC;YAAC;YAC9B,IAAI,oBAAoB,0CAAU,OAAO,EACrC,EAAE,cAAc,GAAG,2BAA2B,EAAE,cAAc;YAGlE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA,GAAA,yCAAkB,AAAD,EAAE,IAAI,sBAAqB;QAC/E,OAAO;YACH,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBAClC,IAAI,CAAC,qBAAqB,GAAG,IAAI;YACrC,CAAC;YACD,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,CAAC,mBAAmB,CAAC,OAAO;gBAChC,IAAI,CAAC,mBAAmB,GAAG,IAAI;YACnC,CAAC;QACL,CAAC;IACL;IACA,qBAAqB;QACjB,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,iBAAiB,KAAK;YAC1B,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAQ;gBACzB,IAAI,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,WAAW,EACxC,iBAAiB,IAAI;YAE7B;YACA,IAAI,CAAC,aAAa,CAAC,iBAAiB,GAAG;QAC3C,CAAC;IACL;IACA,8BAA8B;QAC1B,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE;YACtC,IAAI,CAAC,+BAA+B,GAAG,IAAI,yBAAwB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;gBACxF,WAAW;gBACX,WAAW;gBACX,MAAM;gBACN,QAAQ;YACZ;YACA,IAAI,CAAC,8BAA8B,GAAG,IAAI,yBAAwB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;gBACvF,WAAW;gBACX,WAAW;gBACX,MAAM;gBACN,QAAQ;YACZ;YACA,IAAI,CAAC,8BAA8B,CAAC,YAAY,GAAG,IAAI,oBAAmB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;YACnG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA,GAAA,yCAAkB,AAAD,EAAE,IAAI,sBAAqB;gBACjE,UAAU;oBACN,cAAc;wBAAE,OAAO,IAAI,CAAC,YAAY;oBAAC;oBACzC,oBAAoB;wBAAE,OAAO,IAAI,CAAC,aAAa,CAAC,eAAe,KAAK,0CAAU,OAAO;oBAAC;gBAC1F;gBACA,cAAc,QAAQ,GAAG,CAAC;;;;;yBAKjB,CAAC;gBACV,gBAAgB,QAAQ,GAAG,CAAC;;;;;;;;;;;;;;;;;wBAiBpB,CAAC;YAEb;QACJ,OAAO;YACH,IAAI,IAAI,CAAC,+BAA+B,EAAE;gBACtC,IAAI,CAAC,+BAA+B,CAAC,OAAO;gBAC5C,IAAI,CAAC,+BAA+B,GAAG,IAAI;YAC/C,CAAC;YACD,IAAI,IAAI,CAAC,8BAA8B,EAAE;gBACrC,IAAI,CAAC,8BAA8B,CAAC,OAAO;gBAC3C,IAAI,CAAC,8BAA8B,GAAG,IAAI;YAC9C,CAAC;YACD,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,aAAa,CAAC,OAAO;gBAC1B,IAAI,CAAC,aAAa,GAAG,IAAI;YAC7B,CAAC;QACL,CAAC;IACL;IACA,mBAAmB,QAAQ,EAAE;QACzB,MAAM,gBAAgB,IAAI,CAAC,KAAK,CAAC,UAAU;QAC3C,MAAM,gBAAgB,SAAS,aAAa,CAAC,IAAI;QACjD,MAAM,gBAAgB,SAAS,aAAa;QAC5C,MAAM,gBAAgB,IAAI;QAC1B,MAAM,oBAAoB,SAAS,cAAc;QACjD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAQ;YACzB,cAAc,GAAG,CAAC,KAAK,IAAI,OAAO;QACtC;QAEA,qBAAqB;QACrB,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI;QAC5B,SAAS,cAAc,GAAG,KAAK;QAC/B,SAAS,aAAa,CAAC,IAAI,aAAY,GAAG,GAAG,IAAI;QAEjD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY;QAC9F,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,KAAK,0CAAU,OAAO;QACxH,qDAAqD;QACrD,SAAS,eAAe,CAAC,IAAI,CAAC,+BAA+B;QAC7D,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAQ;YACzB,IAAI,IAAI,QAAQ,EACZ,IAAI,OAAO,GAAG,cAAc,GAAG,CAAC,QAAS,CAAA,AAAC,IAAI,QAAQ,CAAC,WAAW,IAAI,CAAC,IAAI,QAAQ,CAAC,UAAU,IAAI,CAAC,IAAI,QAAQ,CAAC,aAAa,IAAK,CAAC,CAAC,IAAI,QAAQ,CAAC,eAAe,AAAD;QAEvK;QACA,SAAS,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;QAC/B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC1B,SAAS,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM;QAEvC,kDAAkD;QAElD,SAAS,eAAe,CAAC,IAAI,CAAC,8BAA8B;QAC5D,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAQ;YACzB,IAAI,IAAI,QAAQ,EACZ,IAAI,OAAO,GAAG,cAAc,GAAG,CAAC,QAAQ,IAAI,QAAQ,CAAC,WAAW,IAAI,IAAI,QAAQ,CAAC,UAAU,IAAI,CAAC,IAAI,QAAQ,CAAC,aAAa;QAElI;QACA,SAAS,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;QAC/B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC1B,SAAS,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM;QAEvC,UAAU;QACV,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAQ;YACzB,IAAI,OAAO,GAAG,cAAc,GAAG,CAAC;QACpC;QACA,SAAS,aAAa,CAAC,eAAe;QACtC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG;QACxB,SAAS,cAAc,GAAG;IAC9B;IACA,iCAAiC;QAC7B,IAAI,CAAC,UAAU;QACf,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;QACzF,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;IAClG;IACA,gBAAgB,mBAAkB,0CAAU,OAAO,EAAE,QAAQ,KAAK,EAAE;QAChE,IAAI,CAAC,UAAU;QACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS;QAC1E,MAAM,IAAI;YAAC,GAAG,CAAA,GAAA,yCAAW,CAAC;QAAC;QAC3B,EAAE,cAAc,GAAG,EAAE,cAAc,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG;QAC/H,IAAI,qBAAoB,0CAAU,GAAG,EACjC,EAAE,cAAc,GAAG,uBAAuB,EAAE,cAAc;aACvD,IAAI,qBAAoB,0CAAU,OAAO,EAC5C,EAAE,cAAc,GAAG,2BAA2B,EAAE,cAAc;QAElE,IAAI,OACA,EAAE,cAAc,GAAG,oBAAoB,EAAE,cAAc;QAE3D,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAC1B,EAAE,cAAc,GAAG,sBAAsB,EAAE,cAAc;QAE7D,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO;YACtC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,IAAI,sBAAqB;QAC9D,OACI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAA,GAAA,yCAAkB,AAAD,EAAE,IAAI,sBAAqB;IAEhF;IACA,qBAAqB,mBAAkB,0CAAU,OAAO,EAAE,QAAQ,KAAK,EAAE;QACrE,IAAI,CAAC,UAAU;QACf,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE;QACrF,MAAM,IAAI;YAAC,GAAG,CAAA,GAAA,yCAAW,CAAC;QAAC;QAC3B,EAAE,cAAc,GAAG,EAAE,cAAc,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc;QACnF,IAAI,qBAAoB,0CAAU,GAAG,EACjC,EAAE,cAAc,GAAG,uBAAuB,EAAE,cAAc;aACvD,IAAI,qBAAoB,0CAAU,OAAO,EAC5C,EAAE,cAAc,GAAG,2BAA2B,EAAE,cAAc;QAElE,IAAI,OACA,EAAE,cAAc,GAAG,oBAAoB,EAAE,cAAc;QAE3D,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO;YACrC,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,IAAI,sBAAqB;QAC7D,OACI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAA,GAAA,yCAAkB,AAAD,EAAE,IAAI,sBAAqB;IAE/E;IACA,0BAA0B,mBAAkB,0CAAU,OAAO,EAAE,QAAQ,KAAK,EAAE;QACtE,IAAI,CAAC,UAAU;QACf,MAAM,IAAI;YAAC,GAAG,CAAA,GAAA,yCAAe,CAAC;QAAC;QAC/B,IAAI,qBAAoB,0CAAU,GAAG,EACjC,EAAE,cAAc,GAAG,uBAAuB,EAAE,cAAc;aACvD,IAAI,qBAAoB,0CAAU,OAAO,EAC5C,EAAE,cAAc,GAAG,2BAA2B,EAAE,cAAc;QAElE,IAAI,OACA,EAAE,cAAc,GAAG,oBAAoB,EAAE,cAAc;QAE3D,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,oBAAoB,EACrE,EAAE,cAAc,GAAG,sBAAsB,EAAE,cAAc;QAE7D,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,OAAO;YAC1C,IAAI,CAAC,oBAAoB,CAAC,QAAQ,GAAG,IAAI,sBAAqB;QAClE,OACI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAA,GAAA,yCAAkB,AAAD,EAAE,IAAI,sBAAqB;IAEpF;IACA;;;;SAIC,GACL,0BAA0B,CAAC,EAAE;QACrB,MAAM,SAAS,EAAE;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YACxB,MAAM,QAAQ,WAAW;YACzB,IAAI,IAAK,KAAK,IAAI,CAAC,IAAI,OAAO,KAAK,IAAI,CAAC;YACxC,MAAM,IAAI,IAAI,KAAK,GAAG,CAAC;YACvB,MAAM,IAAI,IAAI,KAAK,GAAG,CAAC;YACvB,wBAAwB;YACxB,MAAM,IAAI,KAAK,IAAI,CAAC,IAAK,CAAA,IAAI,IAAI,IAAI,CAAA;YACrC,OAAO,IAAI,CAAC,IAAI,eAAc,GAAG,GAAG;QAExC;QACA,OAAO;IACX;IACA;;;;;SAKC,GACL,uBAAuB,UAAU,EAAE,QAAQ,EAAE;QACzC,MAAM,YAAY,IAAI,KAAK,EAAE,GAAG,WAAW;QAC3C,MAAM,gBAAgB,MAAM;QAC5B,MAAM,aAAa;QACnB,MAAM,UAAU,EAAE;QAClB,IAAI,SAAS;QACb,IAAI,QAAQ;QACZ,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;YACjC,QAAQ,IAAI,CAAC,IAAI,eAAc,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,cAAc,CAAC,KAAK,GAAG,CAAC,QAAQ;YACjG,UAAU;YACV,SAAS;QACb;QACA,OAAO;IACX;IACA,QAAQ,KAAK,EAAE,MAAM,EAAE;QACnB,IAAI,CAAC,UAAU;QACf,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,MAAM,CAAC;QAC9C,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO;QACvC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAC7B,GAAG,SACH;QACJ,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,QAC5B,GAAG,SACH;QACJ,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,QAAQ,GAAG,SAAS;QAC1D,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAC1B,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,QAAQ,GAAG,SAAS;QAE3D,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE;YACtC,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,OAAO;YACpD,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,OAAO;QACvD,CAAC;IACL;IACA,aAAa;QACT,IAAI,CAAC,UAAU,GAAG,IAAI;IAC1B;IAEA,OAAO,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE;QACzD,IAAI,SAAS,YAAY,CAAC,sBAAsB,IAAI,IAAI,CAAC,aAAa,CAAC,eAAe,KAAK,0CAAU,GAAG,IAAI,SAAS,YAAY,CAAC,kBAAkB,IAAI,IAAI,CAAC,aAAa,CAAC,eAAe,KAAK,0CAAU,OAAO,EAAE;YAC9M,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,SAAS,YAAY,CAAC,sBAAsB,GAAG,0CAAU,GAAG,GAAG,SAAS,YAAY,CAAC,kBAAkB,GAAG,0CAAU,OAAO,GAAG,0CAAU,OAAO;YACpL,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;YACzF,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;YAC9F,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;QACvG,CAAC;QACD,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,MAAM,CAAC,iBAAiB;QAC7B,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,KAAK,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,KAAK,IAAI,CAAC,aAAa,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,EACjL,IAAI,CAAC,KAAK;aACP;YACH,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE;gBAC/B,SAAS,eAAe,CAAC,IAAI,CAAC,wBAAwB;gBACtD,SAAS,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;YACnC,CAAC;YACD,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,UAAU,GAAG,KAAK;QAC3B,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB;QACvD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB;QAC3D,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,KAAK,SAAS,UAAU;YACxB,MAAM,GAAG,YAAY,CAAC;YACtB,IAAI,QAAQ,IAAI,EAAE;gBACd,QAAQ,KAAK,CAAC;gBACd,IAAI,CAAC,SAAS,GAAG,KAAK;YAC1B,CAAC;QACL,CAAC;QACD,IAAI,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE;YACrC,SAAS,eAAe,CAAC,IAAI,CAAC,kBAAkB;YAChD,SAAS,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM;YACvC,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,EACpC,IAAI,CAAC,kBAAkB,CAAC;QAEhC,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,aAAa,GAAG,WAAW;YAC3B,GAAG,UAAU,CAAC,IAAI,gBAAgB,EAAE;QACxC,CAAC;QACD,MAAM,YAAY,SAAS,EAAE,CAAC,OAAO;QACrC,SAAS,EAAE,CAAC,OAAO,GAAG,KAAK;QAE3B,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM;QACnC,IAAI,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;QAC5C,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAClE,cAAc;QAElB,IAAI,IAAI,CAAC,KAAK,GAAG,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;YAClD,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;gBAE5B,SAAS,eAAe,CAAC,IAAI,CAAC,qBAAqB;gBACnD,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY;gBAClG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE;gBACrE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;gBAC3E,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG;gBACzE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB;gBAC7G,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW;gBAC3F,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,KAAK,0CAAU,GAAG;gBACnH,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB;gBAC5F,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YACpC,CAAC;YACD,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO;YAC/F,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY;YACxK,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI;YACzI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB;YACvF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB;YACzF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK;YACjJ,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB;YAC1G,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW;YACxF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI;YAC9F,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,iBAAiB,CAAC,KAAK,GAAG,IAAI,eAAc,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc;YACpJ,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAI,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG,cAAc,CAAC,KAAO,KAAK,KAAK,IAAI,CAAC,EAAE;YACnJ,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,GAAG,YAAY,GAAG,KAAK;YAC5E,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO;YACvE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS;YAC3E,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG;YAC1D,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe;YACrG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;YACxE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG;YACtE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB;YACzF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB;YACzG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;YACnE,eAAe;YACf,SAAS,eAAe,CAAC,IAAI,CAAC,mBAAmB;YACjD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;YAC7B,aAAa;YACb,iBAAiB;YACjB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,IAAK;gBAC3D,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,kBAAkB,CAAC,GAAG;oBAAC,IAAI,CAAC,kBAAkB;oBAAE,IAAI,CAAC,mBAAmB;iBAAC;gBACzG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO;gBAC1F,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY;gBACvK,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB;gBACtF,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB;gBACxF,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB;gBACzG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvF,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI;gBAC7F,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAI,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG,cAAc,CAAC,KAAO,KAAK,KAAK,IAAI,CAAC,EAAE;gBAClJ,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,GAAG,YAAY,GAAG,KAAK;gBAC3E,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS;gBAC1E,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,GACrF,CAAA,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,MAAQ,CAAC,AAAD;gBAEzC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,GAAG;gBAC9D,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe;gBACpG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG;gBACxD,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc;gBACjF,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;gBACvE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG;gBACrE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBACxG,SAAS,eAAe,CAAC,IAAI,CAAC,mBAAmB;gBACjD,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;YAEhC;YACA,SAAS,eAAe,CAAC,IAAI,CAAC,wBAAwB;YACtD,MAAM,eAAe,SAAS,SAAS;YACvC,SAAS,SAAS,GAAG,KAAK;YAC1B,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO;YAC5F,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;YACnE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;YAC7B,SAAS,SAAS,GAAG;QACzB,CAAC;QACD,qDAAqD;QACrD,eAAe;QACf,wBAAwB;QACxB,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE;YACtC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC,KAAK,GAAG,IAAI,CAAC,+BAA+B,CAAC,OAAO;YACvH,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,qBAAqB,CAAC,KAAK,GAAG,IAAI,CAAC,8BAA8B,CAAC,OAAO;YACrH,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,0BAA0B,CAAC,KAAK,GAAG,IAAI,CAAC,8BAA8B,CAAC,YAAY;YAC/H,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,KAAK,GAAG,IAAI;QACjF,CAAC;QACD,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO;QACnG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY;QACtG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO;QACzF,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;QAC5E,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG;QAC1E,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAuB;QAC9G,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW;QAC5F,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB;QAC7F,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY;QAClL,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE;QACzE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS;QAC/E,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS;QAC7F,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU;QAC/F,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB;QAC7G,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG;QAC9D,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe;QACzG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe;QACzG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO;QACrG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,CACrE,IAAI,CAAC,aAAa,CAAC,KAAK,EAC1B,mBAAmB;QACrB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa;QACrG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI;QAClG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG;QAC3E,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;YAChB,IACI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EACtB;gBACE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,KAAK;gBACnE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI;gBAClF,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG;YACpF,OAAO,IACH,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,EAC1B;gBACE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI;gBAClE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO;YAC5F,OACI,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC;QAI5F,CAAC;QACD,SAAS,eAAe,CACpB,IAAI,CAAC,cAAc,GAAG,IAAI,GAC1B,WAAW;QAEf,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;QACjC,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,GAAG,QAAQ,CAAC,IAAI,gBAAgB;YAChC,sCAAgB,YAAY,IAAI,IAAI;QACxC,CAAC;QAED,SAAS,EAAE,CAAC,OAAO,GAAG;IAC1B;IACA;;SAEC,GACL,kBAAkB;QACV,IAAI,CAAC,SAAS,GAAG,IAAI;IACzB;IACA;;SAEC,GACL,mBAAmB;QACX,IAAI,CAAC,SAAS,GAAG,KAAK;IAC1B;IACA;;;SAGC,GACL,eAAe,IAAI,EAAE;QACb,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG;YAAC;YAAY;YAAM;YAAS;YAAS;SAAW,CAAC,OAAO,CAAC;IAC7F;IACA;;;SAGC,GACL,eAAe,IAAI,EAAE;QACjB,IAAI,SAAS,eAAe;YACxB,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG;YAC/B,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG;YACpC,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG;QACvC,OAAO,IAAI,SAAS,OAAO;YACvB,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG;YAC/B,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG;YACpC,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG;QACvC,OAAO,IAAI,SAAS,UAAU;YAC1B,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG;YAC/B,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG;YACpC,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG;QACvC,OAAO,IAAI,SAAS,QAAQ;YACxB,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG;YAC/B,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG;YACpC,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG;QACvC,OAAO,IAAI,SAAS,SAAS;YACzB,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG;YAC/B,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG;YACpC,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG;QACvC,CAAC;IAEL;AACJ", "sources": ["src/N8AOPass.js", "src/FullScreenTriangle.js", "src/EffectShader.js", "src/EffectCompositer.js", "src/PoissionBlur.js", "src/DepthDownSample.js", "src/N8AOPostPass.js", "src/BlueNoise.js", "src/compat.js"], "sourcesContent": ["import * as THREE from 'three';\nimport { Pass } from \"three/examples/jsm/postprocessing/Pass.js\";\nimport { FullScreenTriangle } from './FullScreenTriangle.js';\nimport { EffectShader } from './EffectShader.js';\nimport { EffectCompositer } from './EffectCompositer.js';\nimport { PoissionBlur } from './PoissionBlur.js';\nimport { DepthDownSample } from \"./DepthDownSample.js\";\nimport { N8AOPostPass } from './N8AOPostPass.js';\nimport bluenoiseBits from './BlueNoise.js';\nimport { WebGLMultipleRenderTargetsCompat } from './compat.js';\n\n/**\n * \n * @param {*} timerQuery \n * @param {THREE.WebGLRenderer} gl \n * @param {N8AOPass} pass \n */\nfunction checkTimerQuery(timerQuery, gl, pass) {\n    const available = gl.getQueryParameter(timerQuery, gl.QUERY_RESULT_AVAILABLE);\n    if (available) {\n        const elapsedTimeInNs = gl.getQueryParameter(timerQuery, gl.QUERY_RESULT);\n        const elapsedTimeInMs = elapsedTimeInNs / 1000000;\n        pass.lastTime = pass.lastTime === 0 ? elapsedTimeInMs : pass.timeRollingAverage * pass.lastTime + (1 - pass.timeRollingAverage) * elapsedTimeInMs;\n    } else {\n        // If the result is not available yet, check again after a delay\n        setTimeout(() => {\n            checkTimerQuery(timerQuery, gl, pass);\n        }, 1);\n    }\n}\n\nexport const DepthType = {\n    Default: 1,\n    Log: 2,\n    Reverse: 3,\n};\n\nclass N8AOPass extends Pass {\n    /**\n     * \n     * @param {THREE.Scene} scene\n     * @param {THREE.Camera} camera \n     * @param {number} width \n     * @param {number} height\n     *  \n     * @property {THREE.Scene} scene\n     * @property {THREE.Camera} camera\n     * @property {number} width\n     * @property {number} height\n     */\n    constructor(scene, camera, width = 512, height = 512) {\n        super();\n        this.width = width;\n        this.height = height;\n\n        this.clear = true;\n\n        this.camera = camera;\n        this.scene = scene;\n        /**\n         * @type {Proxy & {\n         * aoSamples: number,\n         * aoRadius: number,\n         * denoiseSamples: number,\n         * denoiseRadius: number,\n         * distanceFalloff: number,\n         * intensity: number,\n         * denoiseIterations: number,\n         * renderMode: 0 | 1 | 2 | 3 | 4,\n         * color: THREE.Color,\n         * gammaCorrection: boolean,\n         * depthBufferType: 1 | 2 | 3,\n         * screenSpaceRadius: boolean,\n         * halfRes: boolean,\n         * depthAwareUpsampling: boolean,\n         * autoRenderBeauty: boolean\n         * colorMultiply: boolean\n         * }\n         */\n        this.configuration = new Proxy({\n            aoSamples: 16,\n            aoRadius: 5.0,\n            aoTones: 0.0,\n            denoiseSamples: 8,\n            denoiseRadius: 12,\n            distanceFalloff: 1.0,\n            intensity: 5,\n            denoiseIterations: 2.0,\n            renderMode: 0,\n            biasOffset: 0.0,\n            biasMultiplier: 0.0,\n            color: new THREE.Color(0, 0, 0),\n            gammaCorrection: true,\n            depthBufferType: DepthType.Default,\n            screenSpaceRadius: false,\n            halfRes: false,\n            depthAwareUpsampling: true,\n            autoRenderBeauty: true,\n            colorMultiply: true,\n            transparencyAware: false,\n            stencil: false,\n            accumulate: false\n        }, {\n            set: (target, propName, value) => {\n                const oldProp = target[propName];\n                target[propName] = value;\n                if (value.equals) {\n                    if (!value.equals(oldProp)) {\n                        this.firstFrame();\n                    }\n                } else {\n                    if (oldProp !== value) {\n                        this.firstFrame();\n                    }\n                }\n                if (propName === 'aoSamples' && oldProp !== value) {\n                    this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                }\n                if (propName === 'denoiseSamples' && oldProp !== value) {\n                    this.configureDenoisePass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                }\n                if (propName === \"halfRes\" && oldProp !== value) {\n                    this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                    this.configureHalfResTargets();\n                    this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                    this.setSize(this.width, this.height);\n                }\n                if (propName === \"depthAwareUpsampling\" && oldProp !== value) {\n                    this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                }\n                if (propName === \"transparencyAware\" && oldProp !== value) {\n                    this.autoDetectTransparency = false;\n                    this.configureTransparencyTarget();\n                }\n                if (propName === \"stencil\" && oldProp !== value) {\n                    /*  this.beautyRenderTarget.stencilBuffer = value;\n                      this.beautyRenderTarget.depthTexture.format = value ? THREE.DepthStencilFormat : THREE.DepthFormat;\n                      this.beautyRenderTarget.depthTexture.type = value ? THREE.UnsignedInt248Type : THREE.UnsignedIntType;\n                      this.beautyRenderTarget.depthTexture.needsUpdate = true;\n                      this.beautyRenderTarget.needsUpdate = true;*/\n                    this.beautyRenderTarget.dispose();\n                    this.beautyRenderTarget = new THREE.WebGLRenderTarget(this.width, this.height, {\n                        minFilter: THREE.LinearFilter,\n                        magFilter: THREE.NearestFilter,\n                        type: THREE.HalfFloatType,\n                        format: THREE.RGBAFormat,\n                        stencilBuffer: value\n                    });\n                    this.beautyRenderTarget.depthTexture = new THREE.DepthTexture(this.width, this.height, value ? THREE.UnsignedInt248Type : THREE.UnsignedIntType);\n                    this.beautyRenderTarget.depthTexture.format = value ? THREE.DepthStencilFormat : THREE.DepthFormat;\n                }\n                return true;\n            }\n        });\n        /** @type {THREE.Vector3[]} */\n        this.samples = [];\n        /** @type {THREE.Vector2[]} */\n        this.samplesDenoise = [];\n        this.autoDetectTransparency = true;\n        this.frame = 0;\n        this.lastViewMatrix = new THREE.Matrix4();\n        this.lastProjectionMatrix = new THREE.Matrix4();\n        this.beautyRenderTarget = new THREE.WebGLRenderTarget(this.width, this.height, {\n            minFilter: THREE.LinearFilter,\n            magFilter: THREE.NearestFilter,\n            type: THREE.HalfFloatType,\n            format: THREE.RGBAFormat,\n            stencilBuffer: false\n        });\n        this.beautyRenderTarget.depthTexture = new THREE.DepthTexture(this.width, this.height, THREE.UnsignedIntType);\n        this.beautyRenderTarget.depthTexture.format = THREE.DepthFormat;\n        this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n        this.configureSampleDependentPasses();\n        this.configureHalfResTargets();\n        this.detectTransparency();\n        this.configureTransparencyTarget();\n\n\n        this.writeTargetInternal = new THREE.WebGLRenderTarget(this.width, this.height, {\n            minFilter: THREE.LinearFilter,\n            magFilter: THREE.LinearFilter,\n            depthBuffer: false,\n            format: THREE.RGBAFormat\n        });\n        this.readTargetInternal = new THREE.WebGLRenderTarget(this.width, this.height, {\n            minFilter: THREE.LinearFilter,\n            magFilter: THREE.LinearFilter,\n            depthBuffer: false,\n            format: THREE.RGBAFormat\n        });\n        this.accumulationRenderTarget = new THREE.WebGLRenderTarget(this.width, this.height, {\n            minFilter: THREE.LinearFilter,\n            magFilter: THREE.LinearFilter,\n            depthBuffer: false,\n            format: THREE.RGBAFormat,\n            type: THREE.HalfFloatType,\n            stencilBuffer: false,\n            depthBuffer: false,\n            alpha: true\n        });\n\n\n        /** @type {THREE.DataTexture} */\n        this.bluenoise = //bluenoise;\n            new THREE.DataTexture(\n                bluenoiseBits,\n                128,\n                128\n            );\n        this.accumulationQuad = new FullScreenTriangle(new THREE.ShaderMaterial({\n            uniforms: {\n                frame: { value: 0 },\n                tDiffuse: { value: null }\n            },\n            transparent: true,\n            opacity: 1,\n            vertexShader: `\n             varying vec2 vUv;\n             void main() {\n                 vUv = uv;\n                 gl_Position = vec4(position, 1);\n             }`,\n            fragmentShader: `\n             uniform sampler2D tDiffuse;\n             uniform float frame;\n                varying vec2 vUv;\n                void main() {\n                    vec4 color = texture2D(tDiffuse, vUv);\n                    gl_FragColor = vec4(color.rgb, 1.0 / (frame + 1.0));\n                }\n                `\n        }));\n        this.bluenoise.colorSpace = THREE.NoColorSpace;\n        this.bluenoise.wrapS = THREE.RepeatWrapping;\n        this.bluenoise.wrapT = THREE.RepeatWrapping;\n        this.bluenoise.minFilter = THREE.NearestFilter;\n        this.bluenoise.magFilter = THREE.NearestFilter;\n        this.bluenoise.needsUpdate = true;\n        this.lastTime = 0;\n        this.timeRollingAverage = 0.99;\n        this._r = new THREE.Vector2();\n        this._c = new THREE.Color();\n\n    }\n    configureHalfResTargets() {\n        this.firstFrame();\n        if (this.configuration.halfRes) {\n            this.depthDownsampleTarget = new WebGLMultipleRenderTargetsCompat(\n                this.width / 2,\n                this.height / 2,\n                2\n            );\n\n            if (THREE.REVISION <= 161) {\n                this.depthDownsampleTarget.textures = this.depthDownsampleTarget.texture;\n            }\n            this.depthDownsampleTarget.textures[0].format = THREE.RedFormat;\n            this.depthDownsampleTarget.textures[0].type = THREE.FloatType;\n            this.depthDownsampleTarget.textures[0].minFilter = THREE.NearestFilter;\n            this.depthDownsampleTarget.textures[0].magFilter = THREE.NearestFilter;\n            this.depthDownsampleTarget.textures[0].depthBuffer = false;\n            this.depthDownsampleTarget.textures[1].format = THREE.RGBAFormat;\n            this.depthDownsampleTarget.textures[1].type = THREE.HalfFloatType;\n            this.depthDownsampleTarget.textures[1].minFilter = THREE.NearestFilter;\n            this.depthDownsampleTarget.textures[1].magFilter = THREE.NearestFilter;\n            this.depthDownsampleTarget.textures[1].depthBuffer = false;\n\n            const e = {...DepthDownSample };\n            if (depthBufferType === DepthType.Reverse) {\n                e.fragmentShader = \"#define REVERSEDEPTH\\n\" + e.fragmentShader;\n            }\n\n            this.depthDownsampleQuad = new FullScreenTriangle(new THREE.ShaderMaterial(e));\n        } else {\n            if (this.depthDownsampleTarget) {\n                this.depthDownsampleTarget.dispose();\n                this.depthDownsampleTarget = null;\n            }\n            if (this.depthDownsampleQuad) {\n                this.depthDownsampleQuad.dispose();\n                this.depthDownsampleQuad = null;\n            }\n        }\n    }\n    detectTransparency() {\n        if (this.autoDetectTransparency) {\n            let isTransparency = false;\n            this.scene.traverse((obj) => {\n                if (obj.material && obj.material.transparent) {\n                    isTransparency = true;\n                }\n            });\n            this.configuration.transparencyAware = isTransparency;\n        }\n    }\n    configureTransparencyTarget() {\n        if (this.configuration.transparencyAware) {\n            this.transparencyRenderTargetDWFalse = new THREE.WebGLRenderTarget(this.width, this.height, {\n                minFilter: THREE.LinearFilter,\n                magFilter: THREE.NearestFilter,\n                type: THREE.HalfFloatType,\n                format: THREE.RGBAFormat\n            });\n            this.transparencyRenderTargetDWTrue = new THREE.WebGLRenderTarget(this.width, this.height, {\n                minFilter: THREE.LinearFilter,\n                magFilter: THREE.NearestFilter,\n                type: THREE.HalfFloatType,\n                format: THREE.RGBAFormat\n            });\n            this.transparencyRenderTargetDWTrue.depthTexture = new THREE.DepthTexture(this.width, this.height, THREE.UnsignedIntType);\n            this.depthCopyPass = new FullScreenTriangle(new THREE.ShaderMaterial({\n                uniforms: {\n                    depthTexture: { value: this.depthTexture },\n                    reverseDepthBuffer: { value: this.configuration.depthBufferType === DepthType.Reverse },\n                },\n                vertexShader: /* glsl */ `\n                        varying vec2 vUv;\n                        void main() {\n                            vUv = uv;\n                            gl_Position = vec4(position, 1);\n                        }`,\n                fragmentShader: /* glsl */ `\n                        uniform sampler2D depthTexture;\n                        uniform bool reverseDepthBuffer;\n                        varying vec2 vUv;\n                        void main() {\n                            if (reverseDepthBuffer) {\n                           float d = 1.0 - texture2D(depthTexture, vUv).r;\n                       \n                           d += 0.00001;\n                           gl_FragDepth = 1.0 - d;\n                        } else {\n                            float d = texture2D(depthTexture, vUv).r;\n                            d += 0.00001;\n                            gl_FragDepth = d;\n                        }\n                           gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n                        }\n                        `,\n\n            }));\n        } else {\n            if (this.transparencyRenderTargetDWFalse) {\n                this.transparencyRenderTargetDWFalse.dispose();\n                this.transparencyRenderTargetDWFalse = null;\n            }\n            if (this.transparencyRenderTargetDWTrue) {\n                this.transparencyRenderTargetDWTrue.dispose();\n                this.transparencyRenderTargetDWTrue = null;\n            }\n            if (this.depthCopyPass) {\n                this.depthCopyPass.dispose();\n                this.depthCopyPass = null;\n            }\n        }\n    }\n    renderTransparency(renderer) {\n        const oldBackground = this.scene.background;\n        const oldClearColor = renderer.getClearColor(new THREE.Color());\n        const oldClearAlpha = renderer.getClearAlpha();\n        const oldVisibility = new Map();\n        const oldAutoClearDepth = renderer.autoClearDepth;\n        this.scene.traverse((obj) => {\n            oldVisibility.set(obj, obj.visible);\n        });\n\n        // Override the state\n        this.scene.background = null;\n        renderer.autoClearDepth = false;\n        renderer.setClearColor(new THREE.Color(0, 0, 0), 0);\n\n        this.depthCopyPass.material.uniforms.depthTexture.value = this.beautyRenderTarget.depthTexture;\n        this.depthCopyPass.material.uniforms.reverseDepthBuffer.value = this.configuration.depthBufferType === DepthType.Reverse;\n        // Render out transparent objects WITHOUT depth write\n        renderer.setRenderTarget(this.transparencyRenderTargetDWFalse);\n        this.scene.traverse((obj) => {\n            if (obj.material) {\n                obj.visible = oldVisibility.get(obj) && ((obj.material.transparent && !obj.material.depthWrite && !obj.userData.treatAsOpaque) || !!obj.userData.cannotReceiveAO);\n            }\n        });\n        renderer.clear(true, true, true);\n        this.depthCopyPass.render(renderer);\n        renderer.render(this.scene, this.camera);\n\n        // Render out transparent objects WITH depth write\n\n        renderer.setRenderTarget(this.transparencyRenderTargetDWTrue);\n        this.scene.traverse((obj) => {\n            if (obj.material) {\n                obj.visible = oldVisibility.get(obj) && obj.material.transparent && obj.material.depthWrite && !obj.userData.treatAsOpaque;\n            }\n        });\n        renderer.clear(true, true, true);\n        this.depthCopyPass.render(renderer);\n        renderer.render(this.scene, this.camera);\n\n        // Restore\n        this.scene.traverse((obj) => {\n            obj.visible = oldVisibility.get(obj);\n        });\n        renderer.setClearColor(oldClearColor, oldClearAlpha);\n        this.scene.background = oldBackground;\n        renderer.autoClearDepth = oldAutoClearDepth;\n    }\n    configureSampleDependentPasses() {\n        this.firstFrame();\n        this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n        this.configureDenoisePass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n    }\n    configureAOPass(depthBufferType = DepthType.Default, ortho = false) {\n        this.firstFrame();\n        this.samples = this.generateHemisphereSamples(this.configuration.aoSamples);\n        const e = {...EffectShader };\n        e.fragmentShader = e.fragmentShader.replace(\"16\", this.configuration.aoSamples).replace(\"16.0\", this.configuration.aoSamples + \".0\");\n        if (depthBufferType === DepthType.Log) {\n            e.fragmentShader = \"#define LOGDEPTH\\n\" + e.fragmentShader;\n        } else if (depthBufferType === DepthType.Reverse) {\n            e.fragmentShader = \"#define REVERSEDEPTH\\n\" + e.fragmentShader;\n        }\n        if (ortho) {\n            e.fragmentShader = \"#define ORTHO\\n\" + e.fragmentShader;\n        }\n        if (this.configuration.halfRes) {\n            e.fragmentShader = \"#define HALFRES\\n\" + e.fragmentShader;\n        }\n        if (this.effectShaderQuad) {\n            this.effectShaderQuad.material.dispose();\n            this.effectShaderQuad.material = new THREE.ShaderMaterial(e);\n        } else {\n            this.effectShaderQuad = new FullScreenTriangle(new THREE.ShaderMaterial(e));\n        }\n    }\n    configureDenoisePass(depthBufferType = DepthType.Default, ortho = false) {\n        this.firstFrame();\n        this.samplesDenoise = this.generateDenoiseSamples(this.configuration.denoiseSamples, 11);\n        const p = {...PoissionBlur };\n        p.fragmentShader = p.fragmentShader.replace(\"16\", this.configuration.denoiseSamples);\n        if (depthBufferType === DepthType.Log) {\n            p.fragmentShader = \"#define LOGDEPTH\\n\" + p.fragmentShader;\n        } else if (depthBufferType === DepthType.Reverse) {\n            p.fragmentShader = \"#define REVERSEDEPTH\\n\" + p.fragmentShader;\n        }\n        if (ortho) {\n            p.fragmentShader = \"#define ORTHO\\n\" + p.fragmentShader;\n        }\n        if (this.poissonBlurQuad) {\n            this.poissonBlurQuad.material.dispose();\n            this.poissonBlurQuad.material = new THREE.ShaderMaterial(p);\n        } else {\n            this.poissonBlurQuad = new FullScreenTriangle(new THREE.ShaderMaterial(p));\n        }\n    }\n    configureEffectCompositer(depthBufferType = DepthType.Default, ortho = false) {\n            this.firstFrame();\n            const e = {...EffectCompositer };\n            if (depthBufferType === DepthType.Log) {\n                e.fragmentShader = \"#define LOGDEPTH\\n\" + e.fragmentShader;\n            } else if (depthBufferType === DepthType.Reverse) {\n                e.fragmentShader = \"#define REVERSEDEPTH\\n\" + e.fragmentShader;\n            }\n            if (ortho) {\n                e.fragmentShader = \"#define ORTHO\\n\" + e.fragmentShader;\n            }\n            if (this.configuration.halfRes && this.configuration.depthAwareUpsampling) {\n                e.fragmentShader = \"#define HALFRES\\n\" + e.fragmentShader;\n            }\n            if (this.effectCompositerQuad) {\n                this.effectCompositerQuad.material.dispose();\n                this.effectCompositerQuad.material = new THREE.ShaderMaterial(e);\n            } else {\n                this.effectCompositerQuad = new FullScreenTriangle(new THREE.ShaderMaterial(e));\n            }\n        }\n        /**\n         * \n         * @param {Number} n \n         * @returns {THREE.Vector3[]}\n         */\n    generateHemisphereSamples(n) {\n            const points = [];\n            for (let k = 0; k < n; k++) {\n                const theta = 2.399963 * k;\n                let r = (Math.sqrt(k + 0.5) / Math.sqrt(n));\n                const x = r * Math.cos(theta);\n                const y = r * Math.sin(theta);\n                // Project to hemisphere\n                const z = Math.sqrt(1 - (x * x + y * y));\n                points.push(new THREE.Vector3(x, y, z));\n\n            }\n            return points;\n        }\n        /**\n         * \n         * @param {number} numSamples \n         * @param {number} numRings \n         * @returns {THREE.Vector2[]}\n         */\n    generateDenoiseSamples(numSamples, numRings) {\n        const angleStep = 2 * Math.PI * numRings / numSamples;\n        const invNumSamples = 1.0 / numSamples;\n        const radiusStep = invNumSamples;\n        const samples = [];\n        let radius = invNumSamples;\n        let angle = 0;\n        for (let i = 0; i < numSamples; i++) {\n            samples.push(new THREE.Vector2(Math.cos(angle), Math.sin(angle)).multiplyScalar(Math.pow(radius, 0.75)));\n            radius += radiusStep;\n            angle += angleStep;\n        }\n        return samples;\n    }\n    setSize(width, height) {\n        this.firstFrame();\n        this.width = width;\n        this.height = height;\n        const c = this.configuration.halfRes ? 0.5 : 1;\n        this.beautyRenderTarget.setSize(width, height);\n        this.writeTargetInternal.setSize(width *\n            c, height *\n            c);\n        this.readTargetInternal.setSize(width *\n            c, height *\n            c);\n        this.accumulationRenderTarget.setSize(width * c, height * c);\n        if (this.configuration.halfRes) {\n            this.depthDownsampleTarget.setSize(width * c, height * c);\n        }\n        if (this.configuration.transparencyAware) {\n            this.transparencyRenderTargetDWFalse.setSize(width, height);\n            this.transparencyRenderTargetDWTrue.setSize(width, height);\n        }\n    }\n    firstFrame() {\n        this.needsFrame = true;\n    }\n\n    render(renderer, writeBuffer, readBuffer, deltaTime, maskActive) {\n            if (renderer.capabilities.logarithmicDepthBuffer && this.configuration.depthBufferType !== DepthType.Log || renderer.capabilities.reverseDepthBuffer && this.configuration.depthBufferType !== DepthType.Reverse) {\n                this.configuration.depthBufferType = renderer.capabilities.logarithmicDepthBuffer ? DepthType.Log : renderer.capabilities.reverseDepthBuffer ? DepthType.Reverse : DepthType.Default;\n                this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                this.configureDenoisePass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n            }\n            this.detectTransparency();\n            this.camera.updateMatrixWorld();\n            if (this.lastViewMatrix.equals(this.camera.matrixWorldInverse) && this.lastProjectionMatrix.equals(this.camera.projectionMatrix) && this.configuration.accumulate && !this.needsFrame) {\n                this.frame++;\n            } else {\n                if (this.configuration.accumulate) {\n                    renderer.setRenderTarget(this.accumulationRenderTarget);\n                    renderer.clear(true, true, true);\n                }\n                this.frame = 0;\n                this.needsFrame = false;\n            }\n            this.lastViewMatrix.copy(this.camera.matrixWorldInverse);\n            this.lastProjectionMatrix.copy(this.camera.projectionMatrix);\n            let gl;\n            let ext;\n            let timerQuery;\n            if (this.debugMode) {\n                gl = renderer.getContext();\n                ext = gl.getExtension('EXT_disjoint_timer_query_webgl2');\n                if (ext === null) {\n                    console.error(\"EXT_disjoint_timer_query_webgl2 not available, disabling debug mode.\");\n                    this.debugMode = false;\n                }\n            }\n            if (this.configuration.autoRenderBeauty) {\n                renderer.setRenderTarget(this.beautyRenderTarget);\n                renderer.render(this.scene, this.camera);\n                if (this.configuration.transparencyAware) {\n                    this.renderTransparency(renderer);\n                }\n            }\n            if (this.debugMode) {\n                timerQuery = gl.createQuery();\n                gl.beginQuery(ext.TIME_ELAPSED_EXT, timerQuery);\n            }\n            const xrEnabled = renderer.xr.enabled;\n            renderer.xr.enabled = false;\n\n            this._r.set(this.width, this.height);\n            let trueRadius = this.configuration.aoRadius;\n            if (this.configuration.halfRes && this.configuration.screenSpaceRadius) {\n                trueRadius *= 0.5;\n            }\n            if (this.frame < 1024 / this.configuration.aoSamples) {\n                if (this.configuration.halfRes) {\n\n                    renderer.setRenderTarget(this.depthDownsampleTarget);\n                    this.depthDownsampleQuad.material.uniforms.sceneDepth.value = this.beautyRenderTarget.depthTexture;\n                    this.depthDownsampleQuad.material.uniforms.resolution.value = this._r;\n                    this.depthDownsampleQuad.material.uniforms[\"near\"].value = this.camera.near;\n                    this.depthDownsampleQuad.material.uniforms[\"far\"].value = this.camera.far;\n                    this.depthDownsampleQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n                    this.depthDownsampleQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n                    this.depthDownsampleQuad.material.uniforms[\"logDepth\"].value = this.configuration.depthBufferType === DepthType.Log;\n                    this.depthDownsampleQuad.material.uniforms[\"ortho\"].value = this.camera.isOrthographicCamera;\n                    this.depthDownsampleQuad.render(renderer);\n                }\n                this.effectShaderQuad.material.uniforms[\"sceneDiffuse\"].value = this.beautyRenderTarget.texture;\n                this.effectShaderQuad.material.uniforms[\"sceneDepth\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[0] : this.beautyRenderTarget.depthTexture;\n                this.effectShaderQuad.material.uniforms[\"sceneNormal\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[1] : null;\n                this.effectShaderQuad.material.uniforms[\"projMat\"].value = this.camera.projectionMatrix;\n                this.effectShaderQuad.material.uniforms[\"viewMat\"].value = this.camera.matrixWorldInverse;\n                this.effectShaderQuad.material.uniforms[\"projViewMat\"].value = this.camera.projectionMatrix.clone().multiply(this.camera.matrixWorldInverse.clone());\n                this.effectShaderQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n                this.effectShaderQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n                this.effectShaderQuad.material.uniforms[\"cameraPos\"].value = this.camera.getWorldPosition(new THREE.Vector3());\n                this.effectShaderQuad.material.uniforms['biasAdjustment'].value = new THREE.Vector2(this.configuration.biasOffset, this.configuration.biasMultiplier);\n                this.effectShaderQuad.material.uniforms['resolution'].value = (this.configuration.halfRes ? this._r.clone().multiplyScalar(1 / 2).floor() : this._r);\n                this.effectShaderQuad.material.uniforms['time'].value = performance.now() / 1000;\n                this.effectShaderQuad.material.uniforms['samples'].value = this.samples;\n                this.effectShaderQuad.material.uniforms['bluenoise'].value = this.bluenoise;\n                this.effectShaderQuad.material.uniforms['radius'].value = trueRadius;\n                this.effectShaderQuad.material.uniforms['distanceFalloff'].value = this.configuration.distanceFalloff;\n                this.effectShaderQuad.material.uniforms[\"near\"].value = this.camera.near;\n                this.effectShaderQuad.material.uniforms[\"far\"].value = this.camera.far;\n                this.effectShaderQuad.material.uniforms[\"ortho\"].value = this.camera.isOrthographicCamera;\n                this.effectShaderQuad.material.uniforms[\"screenSpaceRadius\"].value = this.configuration.screenSpaceRadius;\n                this.effectShaderQuad.material.uniforms[\"frame\"].value = this.frame;\n                // Start the AO\n                renderer.setRenderTarget(this.writeTargetInternal);\n                this.effectShaderQuad.render(renderer);\n                // End the AO\n                // Start the blur\n                for (let i = 0; i < this.configuration.denoiseIterations; i++) {\n                    [this.writeTargetInternal, this.readTargetInternal] = [this.readTargetInternal, this.writeTargetInternal];\n                    this.poissonBlurQuad.material.uniforms[\"tDiffuse\"].value = this.readTargetInternal.texture;\n                    this.poissonBlurQuad.material.uniforms[\"sceneDepth\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[0] : this.beautyRenderTarget.depthTexture;\n                    this.poissonBlurQuad.material.uniforms[\"projMat\"].value = this.camera.projectionMatrix;\n                    this.poissonBlurQuad.material.uniforms[\"viewMat\"].value = this.camera.matrixWorldInverse;\n                    this.poissonBlurQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n                    this.poissonBlurQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n                    this.poissonBlurQuad.material.uniforms[\"cameraPos\"].value = this.camera.getWorldPosition(new THREE.Vector3());\n                    this.poissonBlurQuad.material.uniforms['resolution'].value = (this.configuration.halfRes ? this._r.clone().multiplyScalar(1 / 2).floor() : this._r);\n                    this.poissonBlurQuad.material.uniforms['time'].value = performance.now() / 1000;\n                    this.poissonBlurQuad.material.uniforms['blueNoise'].value = this.bluenoise;\n                    this.poissonBlurQuad.material.uniforms['radius'].value = this.configuration.denoiseRadius * (\n                        this.configuration.halfRes ? 1 / 2 : 1\n                    );\n                    this.poissonBlurQuad.material.uniforms['worldRadius'].value = trueRadius;\n                    this.poissonBlurQuad.material.uniforms['distanceFalloff'].value = this.configuration.distanceFalloff;\n                    this.poissonBlurQuad.material.uniforms['index'].value = i;\n                    this.poissonBlurQuad.material.uniforms['poissonDisk'].value = this.samplesDenoise;\n                    this.poissonBlurQuad.material.uniforms[\"near\"].value = this.camera.near;\n                    this.poissonBlurQuad.material.uniforms[\"far\"].value = this.camera.far;\n                    this.poissonBlurQuad.material.uniforms[\"screenSpaceRadius\"].value = this.configuration.screenSpaceRadius;\n                    renderer.setRenderTarget(this.writeTargetInternal);\n                    this.poissonBlurQuad.render(renderer);\n\n                }\n                renderer.setRenderTarget(this.accumulationRenderTarget);\n                const oldAutoClear = renderer.autoClear;\n                renderer.autoClear = false;\n                this.accumulationQuad.material.uniforms[\"tDiffuse\"].value = this.writeTargetInternal.texture;\n                this.accumulationQuad.material.uniforms[\"frame\"].value = this.frame;\n                this.accumulationQuad.render(renderer);\n                renderer.autoClear = oldAutoClear;\n            }\n            // Now, we have the blurred AO in writeTargetInternal\n            // End the blur\n            // Start the composition\n            if (this.configuration.transparencyAware) {\n                this.effectCompositerQuad.material.uniforms[\"transparencyDWFalse\"].value = this.transparencyRenderTargetDWFalse.texture;\n                this.effectCompositerQuad.material.uniforms[\"transparencyDWTrue\"].value = this.transparencyRenderTargetDWTrue.texture;\n                this.effectCompositerQuad.material.uniforms[\"transparencyDWTrueDepth\"].value = this.transparencyRenderTargetDWTrue.depthTexture;\n                this.effectCompositerQuad.material.uniforms[\"transparencyAware\"].value = true;\n            }\n            this.effectCompositerQuad.material.uniforms[\"sceneDiffuse\"].value = this.beautyRenderTarget.texture;\n            this.effectCompositerQuad.material.uniforms[\"sceneDepth\"].value = this.beautyRenderTarget.depthTexture;\n            this.effectCompositerQuad.material.uniforms[\"aoTones\"].value = this.configuration.aoTones;\n            this.effectCompositerQuad.material.uniforms[\"near\"].value = this.camera.near;\n            this.effectCompositerQuad.material.uniforms[\"far\"].value = this.camera.far;\n            this.effectCompositerQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n            this.effectCompositerQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n            this.effectCompositerQuad.material.uniforms[\"ortho\"].value = this.camera.isOrthographicCamera;\n            this.effectCompositerQuad.material.uniforms[\"downsampledDepth\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[0] : this.beautyRenderTarget.depthTexture;\n            this.effectCompositerQuad.material.uniforms[\"resolution\"].value = this._r;\n            this.effectCompositerQuad.material.uniforms[\"blueNoise\"].value = this.bluenoise;\n            this.effectCompositerQuad.material.uniforms[\"intensity\"].value = this.configuration.intensity;\n            this.effectCompositerQuad.material.uniforms[\"renderMode\"].value = this.configuration.renderMode;\n            this.effectCompositerQuad.material.uniforms[\"screenSpaceRadius\"].value = this.configuration.screenSpaceRadius;\n            this.effectCompositerQuad.material.uniforms['radius'].value = trueRadius;\n            this.effectCompositerQuad.material.uniforms['distanceFalloff'].value = this.configuration.distanceFalloff;\n            this.effectCompositerQuad.material.uniforms[\"gammaCorrection\"].value = this.configuration.gammaCorrection;\n            this.effectCompositerQuad.material.uniforms[\"tDiffuse\"].value = this.accumulationRenderTarget.texture;\n            this.effectCompositerQuad.material.uniforms[\"color\"].value = this._c.copy(\n                this.configuration.color\n            ).convertSRGBToLinear();\n            this.effectCompositerQuad.material.uniforms[\"colorMultiply\"].value = this.configuration.colorMultiply;\n            this.effectCompositerQuad.material.uniforms[\"cameraPos\"].value = this.camera.getWorldPosition(new THREE.Vector3());\n            this.effectCompositerQuad.material.uniforms[\"fog\"].value = !!this.scene.fog;\n            if (this.scene.fog) {\n                if (\n                    this.scene.fog.isFog\n                ) {\n                    this.effectCompositerQuad.material.uniforms[\"fogExp\"].value = false;\n                    this.effectCompositerQuad.material.uniforms[\"fogNear\"].value = this.scene.fog.near;\n                    this.effectCompositerQuad.material.uniforms[\"fogFar\"].value = this.scene.fog.far;\n                } else if (\n                    this.scene.fog.isFogExp2\n                ) {\n                    this.effectCompositerQuad.material.uniforms[\"fogExp\"].value = true;\n                    this.effectCompositerQuad.material.uniforms[\"fogDensity\"].value = this.scene.fog.density;\n                } else {\n                    console.error(`Unsupported fog type ${this.scene.fog.constructor.name} in SSAOPass.`);\n                }\n\n\n            }\n            renderer.setRenderTarget(\n                this.renderToScreen ? null :\n                writeBuffer\n            );\n            this.effectCompositerQuad.render(renderer);\n            if (this.debugMode) {\n                gl.endQuery(ext.TIME_ELAPSED_EXT);\n                checkTimerQuery(timerQuery, gl, this);\n            }\n\n            renderer.xr.enabled = xrEnabled;\n        }\n        /**\n         * Enables the debug mode of the AO, meaning the lastTime value will be updated.\n         */\n    enableDebugMode() {\n            this.debugMode = true;\n        }\n        /**\n         * Disables the debug mode of the AO, meaning the lastTime value will not be updated.\n         */\n    disableDebugMode() {\n            this.debugMode = false;\n        }\n        /**\n         * Sets the display mode of the AO\n         * @param {\"Combined\" | \"AO\" | \"No AO\" | \"Split\" | \"Split AO\"} mode - The display mode. \n         */\n    setDisplayMode(mode) {\n            this.configuration.renderMode = [\"Combined\", \"AO\", \"No AO\", \"Split\", \"Split AO\"].indexOf(mode);\n        }\n        /**\n         * \n         * @param {\"Performance\" | \"Low\" | \"Medium\" | \"High\" | \"Ultra\"} mode \n         */\n    setQualityMode(mode) {\n        if (mode === \"Performance\") {\n            this.configuration.aoSamples = 8;\n            this.configuration.denoiseSamples = 4;\n            this.configuration.denoiseRadius = 12;\n        } else if (mode === \"Low\") {\n            this.configuration.aoSamples = 16;\n            this.configuration.denoiseSamples = 4;\n            this.configuration.denoiseRadius = 12;\n        } else if (mode === \"Medium\") {\n            this.configuration.aoSamples = 16;\n            this.configuration.denoiseSamples = 8;\n            this.configuration.denoiseRadius = 12;\n        } else if (mode === \"High\") {\n            this.configuration.aoSamples = 64;\n            this.configuration.denoiseSamples = 8;\n            this.configuration.denoiseRadius = 6;\n        } else if (mode === \"Ultra\") {\n            this.configuration.aoSamples = 64;\n            this.configuration.denoiseSamples = 16;\n            this.configuration.denoiseRadius = 6;\n        }\n\n    }\n}\nexport { N8AOPass, N8AOPostPass };", "import * as THREE from 'three';\n\nclass FullScreenTriangleGeometry extends THREE.BufferGeometry {\n  boundingSphere = new THREE.Sphere();\n\n  constructor() {\n    super()\n    this.setAttribute('position', new THREE.BufferAttribute(new Float32Array([-1, -1, 3, -1, -1, 3]), 2));\n    this.setAttribute('uv', new THREE.BufferAttribute(new Float32Array([0, 0, 2, 0, 0, 2]), 2));\n  }\n\n  computeBoundingSphere() {}\n}\n\nconst _geometry = /* @__PURE__ */ new FullScreenTriangleGeometry();\nconst _camera = /* @__PURE__ */ new THREE.OrthographicCamera();\n\nexport class FullScreenTriangle {\n  constructor(material) {\n    this._mesh = new THREE.Mesh(_geometry, material);\n    this._mesh.frustumCulled = false;\n  }\n\n  render(renderer) {\n    renderer.render(this._mesh, _camera);\n  }\n\n  get material() {\n    return this._mesh.material;\n  }\n\n  set material(value) {\n    this._mesh.material = value;\n  }\n\n  dispose() {\n    this._mesh.material.dispose();\n    this._mesh.geometry.dispose();\n  }\n}\n", "import * as THREE from 'three';\nconst EffectShader = {\n\n    uniforms: {\n\n        'sceneDiffuse': { value: null },\n        'sceneDepth': { value: null },\n        'sceneNormal': { value: null },\n        'projMat': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'viewMat': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'projViewMat': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'projectionMatrixInv': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'viewMatrixInv': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'cameraPos': { value: /* @__PURE__ */ new THREE.Vector3() },\n        'resolution': { value: /* @__PURE__ */ new THREE.Vector2() },\n        'biasAdjustment': { value: /* @__PURE__ */ new THREE.Vector2() },\n        'time': { value: 0.0 },\n        'samples': { value: [] },\n        'bluenoise': { value: null },\n        'distanceFalloff': { value: 1.0 },\n        'radius': { value: 5.0 },\n        'near': { value: 0.1 },\n        'far': { value: 1000.0 },\n        'ortho': { value: false },\n        'screenSpaceRadius': { value: false },\n        'frame': { value: 0.0 }\n    },\n    depthWrite: false,\n    depthTest: false,\n    vertexShader: /* glsl */ `\nvarying vec2 vUv;\nvoid main() {\n  vUv = uv;\n  gl_Position = vec4(position, 1);\n}`,\n\n    fragmentShader: /* glsl */ `\n    #define SAMPLES 16\n    #define FSAMPLES 16.0\nuniform sampler2D sceneDiffuse;\nuniform highp sampler2D sceneNormal;\nuniform highp sampler2D sceneDepth;\nuniform mat4 projectionMatrixInv;\nuniform mat4 viewMatrixInv;\nuniform mat4 projMat;\nuniform mat4 viewMat;\nuniform mat4 projViewMat;\nuniform vec3 cameraPos;\nuniform vec2 resolution;\nuniform vec2 biasAdjustment;\nuniform float time;\nuniform vec3[SAMPLES] samples;\nuniform float radius;\nuniform float distanceFalloff;\nuniform float near;\nuniform float far;\nuniform float frame;\nuniform bool ortho;\nuniform bool screenSpaceRadius;\nuniform sampler2D bluenoise;\n    varying vec2 vUv;\n    highp float linearize_depth(highp float d, highp float zNear,highp float zFar)\n    {\n        return (zFar * zNear) / (zFar - d * (zFar - zNear));\n    }\n    highp float linearize_depth_ortho(highp float d, highp float nearZ, highp float farZ) {\n      return nearZ + (farZ - nearZ) * d;\n    }\n    highp float linearize_depth_log(highp float d, highp float nearZ,highp float farZ) {\n      float depth = pow(2.0, d * log2(farZ + 1.0)) - 1.0;\n      float a = farZ / (farZ - nearZ);\n      float b = farZ * nearZ / (nearZ - farZ);\n      float linDepth = a + b / depth;\n      /*return ortho ? linearize_depth_ortho(\n        linDepth,\n        nearZ,\n        farZ\n      ) :linearize_depth(linDepth, nearZ, farZ);*/\n       #ifdef ORTHO\n\n       return linearize_depth_ortho(d, nearZ, farZ);\n\n        #else\n        return linearize_depth(linDepth, nearZ, farZ);\n        #endif\n    }\n\n    vec3 getWorldPosLog(vec3 posS) {\n      vec2 uv = posS.xy;\n      float z = posS.z;\n      float nearZ =near;\n      float farZ = far;\n      float depth = pow(2.0, z * log2(farZ + 1.0)) - 1.0;\n      float a = farZ / (farZ - nearZ);\n      float b = farZ * nearZ / (nearZ - farZ);\n      float linDepth = a + b / depth;\n      vec4 clipVec = vec4(uv, linDepth, 1.0) * 2.0 - 1.0;\n      vec4 wpos = projectionMatrixInv * clipVec;\n      return wpos.xyz / wpos.w;\n    }\n    vec3 getWorldPos(float depth, vec2 coord) {\n      #ifdef LOGDEPTH\n        #ifndef ORTHO\n          return getWorldPosLog(vec3(coord, depth));\n        #endif\n      #endif\n      float z = depth * 2.0 - 1.0;\n      vec4 clipSpacePosition = vec4(coord * 2.0 - 1.0, z, 1.0);\n      vec4 viewSpacePosition = projectionMatrixInv * clipSpacePosition;\n      // Perspective division\n     vec4 worldSpacePosition = viewSpacePosition;\n     worldSpacePosition.xyz /= worldSpacePosition.w;\n      return worldSpacePosition.xyz;\n  }\n\n  vec3 computeNormal(vec3 worldPos, vec2 vUv) {\n    ivec2 p = ivec2(vUv * resolution);\n    #ifdef REVERSEDEPTH\n    float c0 = 1.0 - texelFetch(sceneDepth, p, 0).x;\n    float l2 = 1.0 - texelFetch(sceneDepth, p - ivec2(2, 0), 0).x;\n    float l1 = 1.0 - texelFetch(sceneDepth, p - ivec2(1, 0), 0).x;\n    float r1 = 1.0 - texelFetch(sceneDepth, p + ivec2(1, 0), 0).x;\n    float r2 = 1.0 - texelFetch(sceneDepth, p + ivec2(2, 0), 0).x;\n    float b2 = 1.0 - texelFetch(sceneDepth, p - ivec2(0, 2), 0).x;\n    float b1 = 1.0 - texelFetch(sceneDepth, p - ivec2(0, 1), 0).x;\n    float t1 = 1.0 - texelFetch(sceneDepth, p + ivec2(0, 1), 0).x;\n    float t2 = 1.0 - texelFetch(sceneDepth, p + ivec2(0, 2), 0).x;\n    #else\n    float c0 = texelFetch(sceneDepth, p, 0).x;\n    float l2 = texelFetch(sceneDepth, p - ivec2(2, 0), 0).x;\n    float l1 = texelFetch(sceneDepth, p - ivec2(1, 0), 0).x;\n    float r1 = texelFetch(sceneDepth, p + ivec2(1, 0), 0).x;\n    float r2 = texelFetch(sceneDepth, p + ivec2(2, 0), 0).x;\n    float b2 = texelFetch(sceneDepth, p - ivec2(0, 2), 0).x;\n    float b1 = texelFetch(sceneDepth, p - ivec2(0, 1), 0).x;\n    float t1 = texelFetch(sceneDepth, p + ivec2(0, 1), 0).x;\n    float t2 = texelFetch(sceneDepth, p + ivec2(0, 2), 0).x;\n    #endif\n\n    float dl = abs((2.0 * l1 - l2) - c0);\n    float dr = abs((2.0 * r1 - r2) - c0);\n    float db = abs((2.0 * b1 - b2) - c0);\n    float dt = abs((2.0 * t1 - t2) - c0);\n\n    vec3 ce = getWorldPos(c0, vUv).xyz;\n\n    vec3 dpdx = (dl < dr) ? ce - getWorldPos(l1, (vUv - vec2(1.0 / resolution.x, 0.0))).xyz\n                          : -ce + getWorldPos(r1, (vUv + vec2(1.0 / resolution.x, 0.0))).xyz;\n    vec3 dpdy = (db < dt) ? ce - getWorldPos(b1, (vUv - vec2(0.0, 1.0 / resolution.y))).xyz\n                          : -ce + getWorldPos(t1, (vUv + vec2(0.0, 1.0 / resolution.y))).xyz;\n\n    return normalize(cross(dpdx, dpdy));\n}\n\nmat3 makeRotationZ(float theta) {\n\tfloat c = cos(theta);\n\tfloat s = sin(theta);\n\treturn mat3(c, - s, 0,\n\t\t\ts,  c, 0,\n\t\t\t0,  0, 1);\n  }\n\nvoid main() {\n      vec4 diffuse = texture2D(sceneDiffuse, vUv);\n      #ifdef REVERSEDEPTH\n      float depth = 1.0 - texture2D(sceneDepth, vUv).x;\n      #else\n      float depth = texture2D(sceneDepth, vUv).x;\n      #endif\n      if (depth == 1.0) {\n        gl_FragColor = vec4(vec3(1.0), 1.0);\n        return;\n      }\n      vec3 worldPos = getWorldPos(depth, vUv);\n      #ifdef HALFRES\n        vec3 normal = texture2D(sceneNormal, vUv).rgb;\n      #else\n        vec3 normal = computeNormal(worldPos, vUv);\n      #endif\n      vec4 noise = texture2D(bluenoise, gl_FragCoord.xy / 128.0);\n      vec2 harmoniousNumbers = vec2(\n        1.618033988749895,\n        1.324717957244746\n      );\n      noise.rg += harmoniousNumbers * frame;\n      noise.rg = fract(noise.rg);\n        vec3 helperVec = vec3(0.0, 1.0, 0.0);\n        if (dot(helperVec, normal) > 0.99) {\n          helperVec = vec3(1.0, 0.0, 0.0);\n        }\n        vec3 tangent = normalize(cross(helperVec, normal));\n        vec3 bitangent = cross(normal, tangent);\n        mediump mat3 tbn = mat3(tangent, bitangent, normal) *  makeRotationZ( noise.r * 3.1415962 * 2.0) ;\n\n      mediump float occluded = 0.0;\n      mediump float totalWeight = 0.0;\n      float radiusToUse = screenSpaceRadius ? distance(\n        worldPos,\n        getWorldPos(depth, vUv +\n          vec2(radius, 0.0) / resolution)\n      ) : radius;\n      float distanceFalloffToUse =screenSpaceRadius ?\n          radiusToUse * distanceFalloff\n      : radiusToUse * distanceFalloff * 0.2;\n      float bias = (min(\n        0.1,\n        distanceFalloffToUse * 0.1\n      ) / near) * fwidth(distance(worldPos, cameraPos)) / radiusToUse;\n      bias = biasAdjustment.x + biasAdjustment.y * bias;\n      mediump float offsetMove = noise.g;\n      mediump float offsetMoveInv = 1.0 / FSAMPLES;\n      float farTimesNear = far * near;\n      float farMinusNear = far - near;\n      \n      for(int i = 0; i < SAMPLES; i++) {\n        mediump vec3 sampleDirection = tbn * samples[i];\n\n        float moveAmt = fract(offsetMove);\n        offsetMove += offsetMoveInv;\n        vec3 samplePos = worldPos + radiusToUse * moveAmt * sampleDirection;\n        vec4 offset = projMat * vec4(samplePos, 1.0);\n        offset.xyz /= offset.w;\n        offset.xyz = offset.xyz * 0.5 + 0.5;\n        \n        if (all(greaterThan(offset.xyz * (1.0 - offset.xyz), vec3(0.0)))) {\n          #ifdef REVERSEDEPTH\n          float sampleDepth = 1.0 - textureLod(sceneDepth, offset.xy, 0.0).x;\n          #else\n          float sampleDepth = textureLod(sceneDepth, offset.xy, 0.0).x;\n          #endif\n\n          /*#ifdef LOGDEPTH\n          float distSample = linearize_depth_log(sampleDepth, near, far);\n      #else\n          #ifdef ORTHO\n              float distSample = near + farMinusNear * sampleDepth;\n          #else\n              float distSample = (farTimesNear) / (far - sampleDepth * farMinusNear);\n          #endif\n      #endif*/\n      #ifdef ORTHO\n          float distSample = near + sampleDepth * farMinusNear;\n      #else\n          #ifdef LOGDEPTH\n              float distSample = linearize_depth_log(sampleDepth, near, far);\n          #else\n              float distSample = (farTimesNear) / (far - sampleDepth * farMinusNear);\n          #endif\n      #endif\n      \n      #ifdef ORTHO\n          float distWorld = near + offset.z * farMinusNear;\n      #else\n          float distWorld = (farTimesNear) / (far - offset.z * farMinusNear);\n      #endif\n          \n          mediump float rangeCheck = smoothstep(0.0, 1.0, distanceFalloffToUse / (abs(distSample - distWorld)));\n          vec2 diff = gl_FragCoord.xy - floor(offset.xy * resolution);\n          occluded += rangeCheck * float(distSample != distWorld) * float(sampleDepth != depth) * step(distSample + bias, distWorld) * step(\n            1.0,\n            dot(diff, diff)\n          );\n          \n          totalWeight ++;\n        }\n      }\n      float occ = clamp(1.0 - occluded / (totalWeight == 0.0 ? 1.0 : totalWeight), 0.0, 1.0);\n      gl_FragColor = vec4(occ, 0.5 + 0.5 * normal);\n}`\n\n\n};\n\nexport { EffectShader };", "import * as THREE from 'three';\nconst EffectCompositer = {\n    uniforms: {\n\n        'sceneDiffuse': { value: null },\n        'sceneDepth': { value: null },\n        'tDiffuse': { value: null },\n        'transparencyDWFalse': { value: null },\n        'transparencyDWTrue': { value: null },\n        'transparencyDWTrueDepth': { value: null },\n        'transparencyAware': { value: false },\n        'projMat': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'viewMat': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'projectionMatrixInv': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'viewMatrixInv': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'cameraPos': { value: /* @__PURE__ */ new THREE.Vector3() },\n        'resolution': { value: /* @__PURE__ */ new THREE.Vector2() },\n        'color': { value: /* @__PURE__ */ new THREE.Vector3(0, 0, 0) },\n        'blueNoise': { value: null },\n        'downsampledDepth': { value: null },\n        'time': { value: 0.0 },\n        'intensity': { value: 10.0 },\n        'renderMode': { value: 0.0 },\n        \"gammaCorrection\": { value: false },\n        \"ortho\": { value: false },\n        \"near\": { value: 0.1 },\n        \"far\": { value: 1000.0 },\n        \"screenSpaceRadius\": { value: false },\n        \"radius\": { value: 0.0 },\n        \"distanceFalloff\": { value: 1.0 },\n        'fog': { value: false },\n        'fogExp': { value: false },\n        'fogDensity': { value: 0.0 },\n        'fogNear': { value: Infinity },\n        'fogFar': { value: Infinity },\n        'colorMultiply': { value: true },\n        'aoTones': { value: 0.0 }\n\n    },\n    depthWrite: false,\n    depthTest: false,\n\n    vertexShader: /* glsl */ `\n\t\tvarying vec2 vUv;\n\t\tvoid main() {\n\t\t\tvUv = uv;\n\t\t\tgl_Position = vec4(position, 1);\n\t\t}`,\n    fragmentShader: /* glsl */ `\n\t\tuniform sampler2D sceneDiffuse;\n    uniform highp sampler2D sceneDepth;\n    uniform highp sampler2D downsampledDepth;\n    uniform highp sampler2D transparencyDWFalse;\n    uniform highp sampler2D transparencyDWTrue;\n    uniform highp sampler2D transparencyDWTrueDepth;\n    uniform sampler2D tDiffuse;\n    uniform sampler2D blueNoise;\n    uniform vec2 resolution;\n    uniform vec3 color;\n    uniform mat4 projectionMatrixInv;\n    uniform mat4 viewMatrixInv;\n    uniform float intensity;\n    uniform float renderMode;\n    uniform float near;\n    uniform float far;\n    uniform float aoTones;\n    uniform bool gammaCorrection;\n    uniform bool ortho;\n    uniform bool screenSpaceRadius;\n    uniform bool fog;\n    uniform bool fogExp;\n    uniform bool colorMultiply;\n    uniform bool transparencyAware;\n    uniform float fogDensity;\n    uniform float fogNear;\n    uniform float fogFar;\n    uniform float radius;\n    uniform float distanceFalloff;\n    uniform vec3 cameraPos;\n    varying vec2 vUv;\n    highp float linearize_depth(highp float d, highp float zNear,highp float zFar)\n    {\n        return (zFar * zNear) / (zFar - d * (zFar - zNear));\n    }\n    highp float linearize_depth_ortho(highp float d, highp float nearZ, highp float farZ) {\n      return nearZ + (farZ - nearZ) * d;\n    }\n    highp float linearize_depth_log(highp float d, highp float nearZ,highp float farZ) {\n      float depth = pow(2.0, d * log2(farZ + 1.0)) - 1.0;\n      float a = farZ / (farZ - nearZ);\n      float b = farZ * nearZ / (nearZ - farZ);\n      float linDepth = a + b / depth;\n      return ortho ? linearize_depth_ortho(\n        linDepth,\n        nearZ,\n        farZ\n      ) :linearize_depth(linDepth, nearZ, farZ);\n    }\n    vec3 getWorldPosLog(vec3 posS) {\n        vec2 uv = posS.xy;\n        float z = posS.z;\n        float nearZ =near;\n        float farZ = far;\n        float depth = pow(2.0, z * log2(farZ + 1.0)) - 1.0;\n        float a = farZ / (farZ - nearZ);\n        float b = farZ * nearZ / (nearZ - farZ);\n        float linDepth = a + b / depth;\n        vec4 clipVec = vec4(uv, linDepth, 1.0) * 2.0 - 1.0;\n        vec4 wpos = projectionMatrixInv * clipVec;\n        return wpos.xyz / wpos.w;\n      }\n      vec3 getWorldPos(float depth, vec2 coord) {\n        #ifdef LOGDEPTH\n          #ifndef ORTHO\n            return getWorldPosLog(vec3(coord, depth));\n          #endif\n        #endif\n      //  }\n        float z = depth * 2.0 - 1.0;\n        vec4 clipSpacePosition = vec4(coord * 2.0 - 1.0, z, 1.0);\n        vec4 viewSpacePosition = projectionMatrixInv * clipSpacePosition;\n        // Perspective division\n       vec4 worldSpacePosition = viewSpacePosition;\n       worldSpacePosition.xyz /= worldSpacePosition.w;\n        return worldSpacePosition.xyz;\n    }\n  \n    vec3 computeNormal(vec3 worldPos, vec2 vUv) {\n      ivec2 p = ivec2(vUv * resolution);\n      #ifdef REVERSEDEPTH\n      float c0 = 1.0 - texelFetch(sceneDepth, p, 0).x;\n      float l2 = 1.0 - texelFetch(sceneDepth, p - ivec2(2, 0), 0).x;\n      float l1 = 1.0 - texelFetch(sceneDepth, p - ivec2(1, 0), 0).x;\n      float r1 = 1.0 - texelFetch(sceneDepth, p + ivec2(1, 0), 0).x;\n      float r2 = 1.0 - texelFetch(sceneDepth, p + ivec2(2, 0), 0).x;\n      float b2 = 1.0 - texelFetch(sceneDepth, p - ivec2(0, 2), 0).x;\n      float b1 = 1.0 - texelFetch(sceneDepth, p - ivec2(0, 1), 0).x;\n      float t1 = 1.0 - texelFetch(sceneDepth, p + ivec2(0, 1), 0).x;\n      float t2 = 1.0 - texelFetch(sceneDepth, p + ivec2(0, 2), 0).x;\n      #else\n      float c0 = texelFetch(sceneDepth, p, 0).x;\n      float l2 = texelFetch(sceneDepth, p - ivec2(2, 0), 0).x;\n      float l1 = texelFetch(sceneDepth, p - ivec2(1, 0), 0).x;\n      float r1 = texelFetch(sceneDepth, p + ivec2(1, 0), 0).x;\n      float r2 = texelFetch(sceneDepth, p + ivec2(2, 0), 0).x;\n      float b2 = texelFetch(sceneDepth, p - ivec2(0, 2), 0).x;\n      float b1 = texelFetch(sceneDepth, p - ivec2(0, 1), 0).x;\n      float t1 = texelFetch(sceneDepth, p + ivec2(0, 1), 0).x;\n      float t2 = texelFetch(sceneDepth, p + ivec2(0, 2), 0).x;\n      #endif\n  \n      float dl = abs((2.0 * l1 - l2) - c0);\n      float dr = abs((2.0 * r1 - r2) - c0);\n      float db = abs((2.0 * b1 - b2) - c0);\n      float dt = abs((2.0 * t1 - t2) - c0);\n  \n      vec3 ce = getWorldPos(c0, vUv).xyz;\n  \n      vec3 dpdx = (dl < dr) ? ce - getWorldPos(l1, (vUv - vec2(1.0 / resolution.x, 0.0))).xyz\n                            : -ce + getWorldPos(r1, (vUv + vec2(1.0 / resolution.x, 0.0))).xyz;\n      vec3 dpdy = (db < dt) ? ce - getWorldPos(b1, (vUv - vec2(0.0, 1.0 / resolution.y))).xyz\n                            : -ce + getWorldPos(t1, (vUv + vec2(0.0, 1.0 / resolution.y))).xyz;\n  \n      return normalize(cross(dpdx, dpdy));\n  }\n\n    #include <common>\n    #include <dithering_pars_fragment>\n    void main() {\n        //vec4 texel = texture2D(tDiffuse, vUv);//vec3(0.0);\n        vec4 sceneTexel = texture2D(sceneDiffuse, vUv);\n        #ifdef REVERSEDEPTH\n        float depth = 1.0 - texture2D(sceneDepth, vUv).x;\n        #else\n        float depth = texture2D(sceneDepth, vUv).x;\n        #endif\n        #ifdef HALFRES \n        vec4 texel;\n        if (depth == 1.0) {\n            texel = vec4(0.0, 0.0, 0.0, 1.0);\n        } else {\n        vec3 worldPos = getWorldPos(depth, vUv);\n        vec3 normal = computeNormal(getWorldPos(depth, vUv), vUv);\n       // vec4 texel = texture2D(tDiffuse, vUv);\n       // Find closest depth;\n       float totalWeight = 0.0;\n       float radiusToUse = screenSpaceRadius ? distance(\n        worldPos,\n        getWorldPos(depth, vUv +\n          vec2(radius, 0.0) / resolution)\n      ) : radius;\n      float distanceFalloffToUse =screenSpaceRadius ?\n          radiusToUse * distanceFalloff\n      : distanceFalloff;\n        for(float x = -1.0; x <= 1.0; x++) {\n            for(float y = -1.0; y <= 1.0; y++) {\n                vec2 offset = vec2(x, y);\n                ivec2 p = ivec2(\n                    (vUv * resolution * 0.5) + offset\n                );\n                vec2 pUv = vec2(p) / (resolution * 0.5);\n                float sampleDepth = texelFetch(downsampledDepth,p, 0).x;\n                vec4 sampleInfo = texelFetch(tDiffuse, p, 0);\n                vec3 normalSample = sampleInfo.gba * 2.0 - 1.0;\n                vec3 worldPosSample = getWorldPos(sampleDepth, pUv);\n                float tangentPlaneDist = abs(dot(worldPosSample - worldPos, normal));\n                float rangeCheck = exp(-1.0 * tangentPlaneDist * (1.0 / distanceFalloffToUse)) * max(dot(normal, normalSample), 0.0);\n                float weight = rangeCheck;\n                totalWeight += weight;\n                texel += sampleInfo * weight;\n            }\n        }\n        if (totalWeight == 0.0) {\n            texel = texture2D(tDiffuse, vUv);\n        } else {\n            texel /= totalWeight;\n        }\n    }\n        #else\n        vec4 texel = texture2D(tDiffuse, vUv);\n        #endif\n\n        #ifdef LOGDEPTH\n        texel.r = clamp(texel.r, 0.0, 1.0);\n        if (texel.r == 0.0) {\n          texel.r = 1.0;\n        }\n        #endif\n     \n        float finalAo = pow(texel.r, intensity);\n        if (aoTones > 0.0) {\n            finalAo = ceil(finalAo * aoTones) / aoTones;\n        }\n        float fogFactor;\n        float fogDepth = distance(\n            cameraPos,\n            getWorldPos(depth, vUv)\n        );\n        if (fog) {\n            if (fogExp) {\n                fogFactor = 1.0 - exp( - fogDensity * fogDensity * fogDepth * fogDepth );\n            } else {\n                fogFactor = smoothstep( fogNear, fogFar, fogDepth );\n            }\n        }\n        if (transparencyAware) {\n            float transparencyDWOff = texture2D(transparencyDWFalse, vUv).a;\n            float transparencyDWOn = texture2D(transparencyDWTrue, vUv).a;\n            float adjustmentFactorOff = transparencyDWOff;\n            #ifdef REVERSEDEPTH\n            float depthSample = 1.0 - texture2D(sceneDepth, vUv).r;\n            float trueDepthSample = 1.0 - texture2D(transparencyDWTrueDepth, vUv).r;\n            #else\n            float depthSample = texture2D(sceneDepth, vUv).r;\n            float trueDepthSample = texture2D(transparencyDWTrueDepth, vUv).r;\n            #endif\n            float adjustmentFactorOn = (1.0 - transparencyDWOn) * (\n                trueDepthSample == depthSample ? 1.0 : 0.0\n            );\n            float adjustmentFactor = max(adjustmentFactorOff, adjustmentFactorOn);\n            finalAo = mix(finalAo, 1.0, adjustmentFactor);\n        }\n        finalAo = mix(finalAo, 1.0, fogFactor);\n        vec3 aoApplied = color * mix(vec3(1.0), sceneTexel.rgb, float(colorMultiply));\n        if (renderMode == 0.0) {\n            gl_FragColor = vec4( mix(sceneTexel.rgb, aoApplied, 1.0 - finalAo), sceneTexel.a);\n        } else if (renderMode == 1.0) {\n            gl_FragColor = vec4( mix(vec3(1.0), aoApplied, 1.0 - finalAo), sceneTexel.a);\n        } else if (renderMode == 2.0) {\n            gl_FragColor = vec4( sceneTexel.rgb, sceneTexel.a);\n        } else if (renderMode == 3.0) {\n            if (vUv.x < 0.5) {\n                gl_FragColor = vec4( sceneTexel.rgb, sceneTexel.a);\n            } else if (abs(vUv.x - 0.5) < 1.0 / resolution.x) {\n                gl_FragColor = vec4(1.0);\n            } else {\n                gl_FragColor = vec4( mix(sceneTexel.rgb, aoApplied, 1.0 - finalAo), sceneTexel.a);\n            }\n        } else if (renderMode == 4.0) {\n            if (vUv.x < 0.5) {\n                gl_FragColor = vec4( sceneTexel.rgb, sceneTexel.a);\n            } else if (abs(vUv.x - 0.5) < 1.0 / resolution.x) {\n                gl_FragColor = vec4(1.0);\n            } else {\n                gl_FragColor = vec4( mix(vec3(1.0), aoApplied, 1.0 - finalAo), sceneTexel.a);\n            }\n        }\n        #include <dithering_fragment>\n        if (gammaCorrection) {\n            gl_FragColor = sRGBTransferOETF(gl_FragColor);\n        }\n    }\n    `\n\n}\nexport { EffectCompositer };", "import * as THREE from 'three';\nconst PoissionBlur = {\n    uniforms: {\n\n        'sceneDiffuse': { value: null },\n        'sceneDepth': { value: null },\n        'tDiffuse': { value: null },\n        'projMat': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'viewMat': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'projectionMatrixInv': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'viewMatrixInv': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'cameraPos': { value: /* @__PURE__ */ new THREE.Vector3() },\n        'resolution': { value: /* @__PURE__ */ new THREE.Vector2() },\n        'time': { value: 0.0 },\n        'r': { value: 5.0 },\n        'blueNoise': { value: null },\n        'radius': { value: 12.0 },\n        'worldRadius': { value: 5.0 },\n        'index': { value: 0.0 },\n        \"poissonDisk\": { value: [] },\n        \"distanceFalloff\": { value: 1.0 },\n        'near': { value: 0.1 },\n        'far': { value: 1000.0 },\n        'screenSpaceRadius': { value: false }\n    },\n    depthWrite: false,\n    depthTest: false,\n\n    vertexShader: /* glsl */ `\n\t\tvarying vec2 vUv;\n\t\tvoid main() {\n\t\t\tvUv = uv;\n\t\t\tgl_Position = vec4(position, 1.0);\n\t\t}`,\n    fragmentShader: /* glsl */ `\n\t\tuniform sampler2D sceneDiffuse;\n    uniform highp sampler2D sceneDepth;\n    uniform sampler2D tDiffuse;\n    uniform sampler2D blueNoise;\n    uniform mat4 projectionMatrixInv;\n    uniform mat4 viewMatrixInv;\n    uniform vec2 resolution;\n    uniform float r;\n    uniform float radius;\n     uniform float worldRadius;\n    uniform float index;\n     uniform float near;\n     uniform float far;\n     uniform float distanceFalloff;\n     uniform bool screenSpaceRadius;\n    varying vec2 vUv;\n\n    highp float linearize_depth(highp float d, highp float zNear,highp float zFar)\n    {\n        highp float z_n = 2.0 * d - 1.0;\n        return 2.0 * zNear * zFar / (zFar + zNear - z_n * (zFar - zNear));\n    }\n    highp float linearize_depth_log(highp float d, highp float nearZ,highp float farZ) {\n     float depth = pow(2.0, d * log2(farZ + 1.0)) - 1.0;\n     float a = farZ / (farZ - nearZ);\n     float b = farZ * nearZ / (nearZ - farZ);\n     float linDepth = a + b / depth;\n     return linearize_depth(linDepth, nearZ, farZ);\n   }\n   highp float linearize_depth_ortho(highp float d, highp float nearZ, highp float farZ) {\n     return nearZ + (farZ - nearZ) * d;\n   }\n   vec3 getWorldPosLog(vec3 posS) {\n     vec2 uv = posS.xy;\n     float z = posS.z;\n     float nearZ =near;\n     float farZ = far;\n     float depth = pow(2.0, z * log2(farZ + 1.0)) - 1.0;\n     float a = farZ / (farZ - nearZ);\n     float b = farZ * nearZ / (nearZ - farZ);\n     float linDepth = a + b / depth;\n     vec4 clipVec = vec4(uv, linDepth, 1.0) * 2.0 - 1.0;\n     vec4 wpos = projectionMatrixInv * clipVec;\n     return wpos.xyz / wpos.w;\n   }\n    vec3 getWorldPos(float depth, vec2 coord) {\n     #ifdef LOGDEPTH\n      #ifndef ORTHO\n          return getWorldPosLog(vec3(coord, depth));\n      #endif\n     #endif\n        \n        float z = depth * 2.0 - 1.0;\n        vec4 clipSpacePosition = vec4(coord * 2.0 - 1.0, z, 1.0);\n        vec4 viewSpacePosition = projectionMatrixInv * clipSpacePosition;\n        // Perspective division\n       vec4 worldSpacePosition = viewSpacePosition;\n       worldSpacePosition.xyz /= worldSpacePosition.w;\n        return worldSpacePosition.xyz;\n    }\n    #include <common>\n    #define NUM_SAMPLES 16\n    uniform vec2 poissonDisk[NUM_SAMPLES];\n    void main() {\n        const float pi = 3.14159;\n        vec2 texelSize = vec2(1.0 / resolution.x, 1.0 / resolution.y);\n        vec2 uv = vUv;\n        vec4 data = texture2D(tDiffuse, vUv);\n        float occlusion = data.r;\n        float baseOcc = data.r;\n        vec3 normal = data.gba * 2.0 - 1.0;\n        float count = 1.0;\n        float d = texture2D(sceneDepth, vUv).x;\n        if (d == 1.0) {\n          gl_FragColor = data;\n          return;\n        }\n        vec3 worldPos = getWorldPos(d, vUv);\n        float size = radius;\n        float angle;\n        if (index == 0.0) {\n             angle = texture2D(blueNoise, gl_FragCoord.xy / 128.0).w * PI2;\n        } else if (index == 1.0) {\n             angle = texture2D(blueNoise, gl_FragCoord.xy / 128.0).z * PI2;\n        } else if (index == 2.0) {\n             angle = texture2D(blueNoise, gl_FragCoord.xy / 128.0).y * PI2;\n        } else {\n             angle = texture2D(blueNoise, gl_FragCoord.xy / 128.0).x * PI2;\n        }\n\n        mat2 rotationMatrix = mat2(cos(angle), -sin(angle), sin(angle), cos(angle));\n        float radiusToUse = screenSpaceRadius ? distance(\n          worldPos,\n          getWorldPos(d, vUv +\n            vec2(worldRadius, 0.0) / resolution)\n        ) : worldRadius;\n        float distanceFalloffToUse =screenSpaceRadius ?\n        radiusToUse * distanceFalloff\n    : radiusToUse * distanceFalloff * 0.2;\n\n        float invDistance = (1.0 / distanceFalloffToUse);\n        for(int i = 0; i < NUM_SAMPLES; i++) {\n            vec2 offset = (rotationMatrix * poissonDisk[i]) * texelSize * size;\n            vec4 dataSample = texture2D(tDiffuse, uv + offset);\n            float occSample = dataSample.r;\n            vec3 normalSample = dataSample.gba * 2.0 - 1.0;\n            float dSample = texture2D(sceneDepth, uv + offset).x;\n            vec3 worldPosSample = getWorldPos(dSample, uv + offset);\n            float tangentPlaneDist = abs(dot(worldPosSample - worldPos, normal));\n            float rangeCheck = float(dSample != 1.0) * exp(-1.0 * tangentPlaneDist * invDistance ) * max(dot(normal, normalSample), 0.0);\n            occlusion += occSample * rangeCheck;\n            count += rangeCheck;\n        }\n        if (count > 0.0) {\n          occlusion /= count;\n        }\n        occlusion = clamp(occlusion, 0.0, 1.0);\n        if (occlusion == 0.0) {\n          occlusion = 1.0;\n        }\n        gl_FragColor = vec4(occlusion, 0.5 + 0.5 * normal);\n    }\n    `\n\n}\nexport { PoissionBlur };", "import * as THREE from 'three';\n\nconst DepthDownSample = {\n    uniforms: {\n        'sceneDepth': { value: null },\n        'resolution': { value: /* @__PURE__ */ new THREE.Vector2() },\n        'near': { value: 0.1 },\n        'far': { value: 1000.0 },\n        'viewMatrixInv': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'projectionMatrixInv': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'logDepth': { value: false },\n        'ortho': { value: false }\n    },\n    depthWrite: false,\n    depthTest: false,\n\n    vertexShader: /* glsl */ `\n    varying vec2 vUv;\n    void main() {\n        vUv = uv;\n        gl_Position = vec4(position, 1);\n    }`,\n    fragmentShader: /* glsl */ `\n    uniform highp sampler2D sceneDepth;\n    uniform vec2 resolution;\n    uniform float near;\n    uniform float far;\n    uniform bool logDepth;\n    uniform bool ortho;\n    uniform mat4 viewMatrixInv;\n    uniform mat4 projectionMatrixInv;\n    varying vec2 vUv;\n    layout(location = 1) out vec4 gNormal;\n    vec3 getWorldPosLog(vec3 posS) {\n        vec2 uv = posS.xy;\n        float z = posS.z;\n        float nearZ =near;\n        float farZ = far;\n        float depth = pow(2.0, z * log2(farZ + 1.0)) - 1.0;\n        float a = farZ / (farZ - nearZ);\n        float b = farZ * nearZ / (nearZ - farZ);\n        float linDepth = a + b / depth;\n        vec4 clipVec = vec4(uv, linDepth, 1.0) * 2.0 - 1.0;\n        vec4 wpos = projectionMatrixInv * clipVec;\n        return wpos.xyz / wpos.w;\n      }\n      vec3 getWorldPos(float depth, vec2 coord) {\n        if (logDepth && !ortho) {\n          return getWorldPosLog(vec3(coord, depth));\n        }\n        float z = depth * 2.0 - 1.0;\n        vec4 clipSpacePosition = vec4(coord * 2.0 - 1.0, z, 1.0);\n        vec4 viewSpacePosition = projectionMatrixInv * clipSpacePosition;\n        // Perspective division\n       vec4 worldSpacePosition = viewSpacePosition;\n       worldSpacePosition.xyz /= worldSpacePosition.w;\n        return worldSpacePosition.xyz;\n    }\n  \n    vec3 computeNormal(vec3 worldPos, vec2 vUv) {\n      ivec2 p = ivec2(vUv * resolution);\n      #ifdef REVERSEDEPTH\n      float c0 = 1.0 - texelFetch(sceneDepth, p, 0).x;\n      float l2 = 1.0 - texelFetch(sceneDepth, p - ivec2(2, 0), 0).x;\n      float l1 = 1.0 - texelFetch(sceneDepth, p - ivec2(1, 0), 0).x;\n      float r1 = 1.0 - texelFetch(sceneDepth, p + ivec2(1, 0), 0).x;\n      float r2 = 1.0 - texelFetch(sceneDepth, p + ivec2(2, 0), 0).x;\n      float b2 = 1.0 - texelFetch(sceneDepth, p - ivec2(0, 2), 0).x;\n      float b1 = 1.0 - texelFetch(sceneDepth, p - ivec2(0, 1), 0).x;\n      float t1 = 1.0 - texelFetch(sceneDepth, p + ivec2(0, 1), 0).x;\n      float t2 = 1.0 - texelFetch(sceneDepth, p + ivec2(0, 2), 0).x;\n      #else\n      float c0 = texelFetch(sceneDepth, p, 0).x;\n      float l2 = texelFetch(sceneDepth, p - ivec2(2, 0), 0).x;\n      float l1 = texelFetch(sceneDepth, p - ivec2(1, 0), 0).x;\n      float r1 = texelFetch(sceneDepth, p + ivec2(1, 0), 0).x;\n      float r2 = texelFetch(sceneDepth, p + ivec2(2, 0), 0).x;\n      float b2 = texelFetch(sceneDepth, p - ivec2(0, 2), 0).x;\n      float b1 = texelFetch(sceneDepth, p - ivec2(0, 1), 0).x;\n      float t1 = texelFetch(sceneDepth, p + ivec2(0, 1), 0).x;\n      float t2 = texelFetch(sceneDepth, p + ivec2(0, 2), 0).x;\n      #endif\n  \n      float dl = abs((2.0 * l1 - l2) - c0);\n      float dr = abs((2.0 * r1 - r2) - c0);\n      float db = abs((2.0 * b1 - b2) - c0);\n      float dt = abs((2.0 * t1 - t2) - c0);\n  \n      vec3 ce = getWorldPos(c0, vUv).xyz;\n  \n      vec3 dpdx = (dl < dr) ? ce - getWorldPos(l1, (vUv - vec2(1.0 / resolution.x, 0.0))).xyz\n                            : -ce + getWorldPos(r1, (vUv + vec2(1.0 / resolution.x, 0.0))).xyz;\n      vec3 dpdy = (db < dt) ? ce - getWorldPos(b1, (vUv - vec2(0.0, 1.0 / resolution.y))).xyz\n                            : -ce + getWorldPos(t1, (vUv + vec2(0.0, 1.0 / resolution.y))).xyz;\n  \n      return normalize(cross(dpdx, dpdy));\n  }\n    void main() {\n        vec2 uv = vUv - vec2(0.5) / resolution;\n        vec2 pixelSize = vec2(1.0) / resolution;\n        highp vec2[4] uvSamples;\n        uvSamples[0] = uv;\n        uvSamples[1] = uv + vec2(pixelSize.x, 0.0);\n        uvSamples[2] = uv + vec2(0.0, pixelSize.y);\n        uvSamples[3] = uv + pixelSize;\n        #ifdef REVERSEDEPTH\n        float depth00 = 1.0 - texture2D(sceneDepth, uvSamples[0]).r;\n        float depth10 = 1.0 - texture2D(sceneDepth, uvSamples[1]).r;\n        float depth01 = 1.0 - texture2D(sceneDepth, uvSamples[2]).r;\n        float depth11 = 1.0 - texture2D(sceneDepth, uvSamples[3]).r;\n        #else\n        float depth00 = texture2D(sceneDepth, uvSamples[0]).r;\n        float depth10 = texture2D(sceneDepth, uvSamples[1]).r;\n        float depth01 = texture2D(sceneDepth, uvSamples[2]).r;\n        float depth11 = texture2D(sceneDepth, uvSamples[3]).r;\n        #endif\n        float minDepth = min(min(depth00, depth10), min(depth01, depth11));\n        float maxDepth = max(max(depth00, depth10), max(depth01, depth11));\n        float targetDepth = minDepth;\n        // Checkerboard pattern to avoid artifacts\n        if (mod(gl_FragCoord.x + gl_FragCoord.y, 2.0) > 0.5) { \n            targetDepth = maxDepth;\n        }\n        int chosenIndex = 0;\n        float[4] samples;\n        samples[0] = depth00;\n        samples[1] = depth10;\n        samples[2] = depth01;\n        samples[3] = depth11;\n        for(int i = 0; i < 4; ++i) {\n            if (samples[i] == targetDepth) {\n                chosenIndex = i;\n                break;\n            }\n        }\n        gl_FragColor = vec4(samples[chosenIndex], 0.0, 0.0, 1.0);\n        gNormal = vec4(computeNormal(\n            getWorldPos(samples[chosenIndex], uvSamples[chosenIndex]), uvSamples[chosenIndex]\n        ), 0.0);\n    }`\n};\n\nexport { DepthDownSample };", "import * as THREE from 'three';\nimport { Pass } from \"postprocessing\";\nimport { FullScreenTriangle } from \"./FullScreenTriangle.js\";\nimport { EffectShader } from './EffectShader.js';\nimport { EffectCompositer } from './EffectCompositer.js';\nimport { PoissionBlur } from './PoissionBlur.js';\nimport { DepthDownSample } from \"./DepthDownSample.js\";\nimport bluenoiseBits from './BlueNoise.js';\nimport { N8AOPass, DepthType } from './N8AOPass.js';\nimport { WebGLMultipleRenderTargetsCompat } from './compat.js';\n\n/**\n * \n * @param {*} timerQuery \n * @param {THREE.WebGLRenderer} gl \n * @param {N8AOPostPass | N8AOPass} pass \n */\nfunction checkTimerQuery(timerQuery, gl, pass) {\n    const available = gl.getQueryParameter(timerQuery, gl.QUERY_RESULT_AVAILABLE);\n    if (available) {\n        const elapsedTimeInNs = gl.getQueryParameter(timerQuery, gl.QUERY_RESULT);\n        const elapsedTimeInMs = elapsedTimeInNs / 1000000;\n        pass.lastTime = pass.lastTime === 0 ? elapsedTimeInMs : pass.timeRollingAverage * pass.lastTime + (1 - pass.timeRollingAverage) * elapsedTimeInMs;\n    } else {\n        // If the result is not available yet, check again after a delay\n        setTimeout(() => {\n            checkTimerQuery(timerQuery, gl, pass);\n        }, 1);\n    }\n}\nclass N8AOPostPass extends Pass {\n    /**\n     * \n     * @param {THREE.Scene} scene\n     * @param {THREE.Camera} camera \n     * @param {number} width \n     * @param {number} height\n     *  \n     * @property {THREE.Scene} scene\n     * @property {THREE.Camera} camera\n     * @property {number} width\n     * @property {number} height\n     */\n    constructor(scene, camera, width = 512, height = 512) {\n        super();\n        this.width = width;\n        this.height = height;\n\n        this.clear = true;\n\n        this.camera = camera;\n        this.scene = scene;\n        /**\n         * @type {Proxy & {\n         * aoSamples: number,\n         * aoRadius: number,\n         * denoiseSamples: number,\n         * denoiseRadius: number,\n         * distanceFalloff: number,\n         * intensity: number,\n         * denoiseIterations: number,\n         * renderMode: 0 | 1 | 2 | 3 | 4,\n         * color: THREE.Color,\n         * gammaCorrection: boolean,\n         * depthBufferType: 1 | 2 | 3,\n         * screenSpaceRadius: boolean,\n         * halfRes: boolean,\n         * depthAwareUpsampling: boolean\n         * colorMultiply: boolean\n         * }\n         */\n        this.autosetGamma = true;\n        this.configuration = new Proxy({\n            aoSamples: 16,\n            aoRadius: 5.0,\n            aoTones: 0.0,\n            denoiseSamples: 8,\n            denoiseRadius: 12,\n            distanceFalloff: 1.0,\n            intensity: 5,\n            denoiseIterations: 2.0,\n            renderMode: 0,\n            biasOffset: 0.0,\n            biasMultiplier: 0.0,\n            color: new THREE.Color(0, 0, 0),\n            gammaCorrection: true,\n            depthBufferType: DepthType.Default,\n            screenSpaceRadius: false,\n            halfRes: false,\n            depthAwareUpsampling: true,\n            colorMultiply: true,\n            transparencyAware: false,\n            accumulate: false\n        }, {\n            set: (target, propName, value) => {\n                const oldProp = target[propName];\n                target[propName] = value;\n                if (value.equals) {\n                    if (!value.equals(oldProp)) {\n                        this.firstFrame();\n                    }\n                } else {\n                    if (oldProp !== value) {\n                        this.firstFrame();\n                    }\n                }\n                if (propName === 'aoSamples' && oldProp !== value) {\n                    this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                }\n                if (propName === 'denoiseSamples' && oldProp !== value) {\n                    this.configureDenoisePass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                }\n                if (propName === \"halfRes\" && oldProp !== value) {\n                    this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                    this.configureHalfResTargets();\n                    this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                    this.setSize(this.width, this.height);\n                }\n                if (propName === \"depthAwareUpsampling\" && oldProp !== value) {\n                    this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                }\n                if (propName === 'gammaCorrection') {\n                    this.autosetGamma = false;\n                }\n                if (propName === \"transparencyAware\" && oldProp !== value) {\n                    this.autoDetectTransparency = false;\n                    this.configureTransparencyTarget();\n                }\n                return true;\n            }\n        });\n        /** @type {THREE.Vector3[]} */\n        this.samples = [];\n        /** @type {THREE.Vector2[]} */\n        this.samplesDenoise = [];\n        this.autoDetectTransparency = true;\n        this.frames = 0;\n        this.lastViewMatrix = new THREE.Matrix4();\n        this.lastProjectionMatrix = new THREE.Matrix4();\n        this.configureEffectCompositer(this.configuration.depthBufferType);\n        this.configureSampleDependentPasses();\n        this.configureHalfResTargets();\n        this.detectTransparency();\n        this.configureTransparencyTarget();\n\n        //   this.effectCompisterQuad = new FullScreenTriangle(new THREE.ShaderMaterial(EffectCompositer));\n        this.copyQuad = new FullScreenTriangle(new THREE.ShaderMaterial({\n            uniforms: {\n                tDiffuse: {\n                    value: null\n                }\n            },\n            depthWrite: false,\n            vertexShader: `\n            varying vec2 vUv;\n            void main() {\n                vUv = uv;\n                gl_Position = vec4(position, 1);\n            }\n            `,\n            fragmentShader: `\n            uniform sampler2D tDiffuse;\n            varying vec2 vUv;\n            void main() {\n                gl_FragColor = texture2D(tDiffuse, vUv);\n            }\n            `\n        }))\n        this.writeTargetInternal = new THREE.WebGLRenderTarget(this.width, this.height, {\n            minFilter: THREE.LinearFilter,\n            magFilter: THREE.LinearFilter,\n            depthBuffer: false,\n            format: THREE.RGBAFormat\n        });\n        this.readTargetInternal = new THREE.WebGLRenderTarget(this.width, this.height, {\n            minFilter: THREE.LinearFilter,\n            magFilter: THREE.LinearFilter,\n            depthBuffer: false,\n            format: THREE.RGBAFormat\n        });\n        this.outputTargetInternal = new THREE.WebGLRenderTarget(this.width, this.height, {\n            minFilter: THREE.LinearFilter,\n            magFilter: THREE.LinearFilter,\n            depthBuffer: false\n        });\n        this.accumulationRenderTarget = new THREE.WebGLRenderTarget(this.width, this.height, {\n            minFilter: THREE.LinearFilter,\n            magFilter: THREE.LinearFilter,\n            depthBuffer: false,\n            format: THREE.RGBAFormat,\n            type: THREE.HalfFloatType,\n            stencilBuffer: false,\n            depthBuffer: false,\n            alpha: true\n        });\n        this.accumulationQuad = new FullScreenTriangle(new THREE.ShaderMaterial({\n            uniforms: {\n                frame: { value: 0 },\n                tDiffuse: { value: null }\n            },\n            transparent: true,\n            opacity: 1,\n            vertexShader: `\n             varying vec2 vUv;\n             void main() {\n                 vUv = uv;\n                 gl_Position = vec4(position, 1);\n             }`,\n            fragmentShader: `\n             uniform sampler2D tDiffuse;\n             uniform float frame;\n                varying vec2 vUv;\n                void main() {\n                    vec4 color = texture2D(tDiffuse, vUv);\n                    gl_FragColor = vec4(color.rgb, 1.0 / (frame + 1.0));\n                }\n                `\n        }));\n\n\n        /** @type {THREE.DataTexture} */\n        this.bluenoise = //bluenoise;\n            new THREE.DataTexture(\n                bluenoiseBits,\n                128,\n                128\n            );\n        this.bluenoise.colorSpace = THREE.NoColorSpace;\n        this.bluenoise.wrapS = THREE.RepeatWrapping;\n        this.bluenoise.wrapT = THREE.RepeatWrapping;\n        this.bluenoise.minFilter = THREE.NearestFilter;\n        this.bluenoise.magFilter = THREE.NearestFilter;\n        this.bluenoise.needsUpdate = true;\n        this.lastTime = 0;\n        this.timeRollingAverage = 0.99;\n        this.needsDepthTexture = true;\n        this.needsSwap = true;\n        this._r = new THREE.Vector2();\n        this._c = new THREE.Color();\n\n\n\n    }\n    configureHalfResTargets() {\n        this.firstFrame();\n\n        if (this.configuration.halfRes) {\n            this.depthDownsampleTarget = new WebGLMultipleRenderTargetsCompat(\n                this.width / 2,\n                this.height / 2,\n                2\n            );\n\n            if (THREE.REVISION <= 161) {\n                this.depthDownsampleTarget.textures = this.depthDownsampleTarget.texture;\n            }\n\n            this.depthDownsampleTarget.textures[0].format = THREE.RedFormat;\n            this.depthDownsampleTarget.textures[0].type = THREE.FloatType;\n            this.depthDownsampleTarget.textures[0].minFilter = THREE.NearestFilter;\n            this.depthDownsampleTarget.textures[0].magFilter = THREE.NearestFilter;\n            this.depthDownsampleTarget.textures[0].depthBuffer = false;\n            this.depthDownsampleTarget.textures[1].format = THREE.RGBAFormat;\n            this.depthDownsampleTarget.textures[1].type = THREE.HalfFloatType;\n            this.depthDownsampleTarget.textures[1].minFilter = THREE.NearestFilter;\n            this.depthDownsampleTarget.textures[1].magFilter = THREE.NearestFilter;\n            this.depthDownsampleTarget.textures[1].depthBuffer = false;\n\n\n            this.depthDownsampleQuad = new FullScreenTriangle(new THREE.ShaderMaterial(DepthDownSample));\n        } else {\n            if (this.depthDownsampleTarget) {\n                this.depthDownsampleTarget.dispose();\n                this.depthDownsampleTarget = null;\n            }\n            if (this.depthDownsampleQuad) {\n                this.depthDownsampleQuad.dispose();\n                this.depthDownsampleQuad = null;\n            }\n        }\n    }\n    detectTransparency() {\n        if (this.autoDetectTransparency) {\n            let isTransparency = false;\n            this.scene.traverse((obj) => {\n                if (obj.material && obj.material.transparent) {\n                    isTransparency = true;\n                }\n            });\n            if (isTransparency) {\n                this.configuration.transparencyAware = true;\n            }\n        }\n    }\n    configureTransparencyTarget() {\n        if (this.configuration.transparencyAware) {\n            this.transparencyRenderTargetDWFalse = new THREE.WebGLRenderTarget(this.width, this.height, {\n                minFilter: THREE.LinearFilter,\n                magFilter: THREE.NearestFilter,\n                type: THREE.HalfFloatType,\n                format: THREE.RGBAFormat\n            });\n            this.transparencyRenderTargetDWTrue = new THREE.WebGLRenderTarget(this.width, this.height, {\n                minFilter: THREE.LinearFilter,\n                magFilter: THREE.NearestFilter,\n                type: THREE.HalfFloatType,\n                format: THREE.RGBAFormat\n            });\n            this.transparencyRenderTargetDWTrue.depthTexture = new THREE.DepthTexture(this.width, this.height, THREE.UnsignedIntType);\n            this.depthCopyPass = new FullScreenTriangle(new THREE.ShaderMaterial({\n                uniforms: {\n                    depthTexture: { value: this.depthTexture },\n                    reverseDepthBuffer: { value: this.configuration.depthBufferType === DepthType.Reverse },\n                },\n                vertexShader: /* glsl */ `\n            varying vec2 vUv;\n            void main() {\n                vUv = uv;\n                gl_Position = vec4(position, 1);\n            }`,\n                fragmentShader: /* glsl */ `\n            uniform sampler2D depthTexture;\n            uniform bool reverseDepthBuffer;\n            varying vec2 vUv;\n            void main() {\n                if (reverseDepthBuffer) {\n               float d = 1.0 - texture2D(depthTexture, vUv).r;\n           \n               d += 0.00001;\n               gl_FragDepth = 1.0 - d;\n            } else {\n                float d = texture2D(depthTexture, vUv).r;\n                d += 0.00001;\n                gl_FragDepth = d;\n            }\n               gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n            }\n            `,\n\n            }));\n        } else {\n            if (this.transparencyRenderTargetDWFalse) {\n                this.transparencyRenderTargetDWFalse.dispose();\n                this.transparencyRenderTargetDWFalse = null;\n            }\n            if (this.transparencyRenderTargetDWTrue) {\n                this.transparencyRenderTargetDWTrue.dispose();\n                this.transparencyRenderTargetDWTrue = null;\n            }\n            if (this.depthCopyPass) {\n                this.depthCopyPass.dispose();\n                this.depthCopyPass = null;\n            }\n        }\n    }\n    renderTransparency(renderer) {\n        const oldBackground = this.scene.background;\n        const oldClearColor = renderer.getClearColor(new THREE.Color());\n        const oldClearAlpha = renderer.getClearAlpha();\n        const oldVisibility = new Map();\n        const oldAutoClearDepth = renderer.autoClearDepth;\n        this.scene.traverse((obj) => {\n            oldVisibility.set(obj, obj.visible);\n        });\n\n        // Override the state\n        this.scene.background = null;\n        renderer.autoClearDepth = false;\n        renderer.setClearColor(new THREE.Color(0, 0, 0), 0);\n\n        this.depthCopyPass.material.uniforms.depthTexture.value = this.depthTexture;\n        this.depthCopyPass.material.uniforms.reverseDepthBuffer.value = this.configuration.depthBufferType === DepthType.Reverse;\n        // Render out transparent objects WITHOUT depth write\n        renderer.setRenderTarget(this.transparencyRenderTargetDWFalse);\n        this.scene.traverse((obj) => {\n            if (obj.material) {\n                obj.visible = oldVisibility.get(obj) && ((obj.material.transparent && !obj.material.depthWrite && !obj.userData.treatAsOpaque) || !!obj.userData.cannotReceiveAO);\n            }\n        });\n        renderer.clear(true, true, true);\n        this.depthCopyPass.render(renderer);\n        renderer.render(this.scene, this.camera);\n\n        // Render out transparent objects WITH depth write\n\n        renderer.setRenderTarget(this.transparencyRenderTargetDWTrue);\n        this.scene.traverse((obj) => {\n            if (obj.material) {\n                obj.visible = oldVisibility.get(obj) && obj.material.transparent && obj.material.depthWrite && !obj.userData.treatAsOpaque;\n            }\n        });\n        renderer.clear(true, true, true);\n        this.depthCopyPass.render(renderer);\n        renderer.render(this.scene, this.camera);\n\n        // Restore\n        this.scene.traverse((obj) => {\n            obj.visible = oldVisibility.get(obj);\n        });\n        renderer.setClearColor(oldClearColor, oldClearAlpha);\n        this.scene.background = oldBackground;\n        renderer.autoClearDepth = oldAutoClearDepth;\n    }\n    configureSampleDependentPasses() {\n        this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n        this.configureDenoisePass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n    }\n    configureAOPass(depthBufferType = DepthType.Default, ortho = false) {\n        this.firstFrame();\n        this.samples = this.generateHemisphereSamples(this.configuration.aoSamples);\n        const e = {...EffectShader };\n        e.fragmentShader = e.fragmentShader.replace(\"16\", this.configuration.aoSamples).replace(\"16.0\", this.configuration.aoSamples + \".0\");\n        if (depthBufferType === DepthType.Log) {\n            e.fragmentShader = \"#define LOGDEPTH\\n\" + e.fragmentShader;\n        } else if (depthBufferType === DepthType.Reverse) {\n            e.fragmentShader = \"#define REVERSEDEPTH\\n\" + e.fragmentShader;\n        }\n        if (ortho) {\n            e.fragmentShader = \"#define ORTHO\\n\" + e.fragmentShader;\n        }\n        if (this.configuration.halfRes) {\n            e.fragmentShader = \"#define HALFRES\\n\" + e.fragmentShader;\n        }\n        if (this.effectShaderQuad) {\n            this.effectShaderQuad.material.dispose();\n            this.effectShaderQuad.material = new THREE.ShaderMaterial(e);\n        } else {\n            this.effectShaderQuad = new FullScreenTriangle(new THREE.ShaderMaterial(e));\n        }\n    }\n    configureDenoisePass(depthBufferType = DepthType.Default, ortho = false) {\n        this.firstFrame();\n        this.samplesDenoise = this.generateDenoiseSamples(this.configuration.denoiseSamples, 11);\n        const p = {...PoissionBlur };\n        p.fragmentShader = p.fragmentShader.replace(\"16\", this.configuration.denoiseSamples);\n        if (depthBufferType === DepthType.Log) {\n            p.fragmentShader = \"#define LOGDEPTH\\n\" + p.fragmentShader;\n        } else if (depthBufferType === DepthType.Reverse) {\n            p.fragmentShader = \"#define REVERSEDEPTH\\n\" + p.fragmentShader;\n        }\n        if (ortho) {\n            p.fragmentShader = \"#define ORTHO\\n\" + p.fragmentShader;\n        }\n        if (this.poissonBlurQuad) {\n            this.poissonBlurQuad.material.dispose();\n            this.poissonBlurQuad.material = new THREE.ShaderMaterial(p);\n        } else {\n            this.poissonBlurQuad = new FullScreenTriangle(new THREE.ShaderMaterial(p));\n        }\n    }\n    configureEffectCompositer(depthBufferType = DepthType.Default, ortho = false) {\n            this.firstFrame();\n\n            const e = {...EffectCompositer };\n            if (depthBufferType === DepthType.Log) {\n                e.fragmentShader = \"#define LOGDEPTH\\n\" + e.fragmentShader;\n            } else if (depthBufferType === DepthType.Reverse) {\n                e.fragmentShader = \"#define REVERSEDEPTH\\n\" + e.fragmentShader;\n            }\n            if (ortho) {\n                e.fragmentShader = \"#define ORTHO\\n\" + e.fragmentShader;\n            }\n            if (this.configuration.halfRes && this.configuration.depthAwareUpsampling) {\n                e.fragmentShader = \"#define HALFRES\\n\" + e.fragmentShader;\n            }\n            if (this.effectCompositerQuad) {\n                this.effectCompositerQuad.material.dispose();\n                this.effectCompositerQuad.material = new THREE.ShaderMaterial(e);\n            } else {\n                this.effectCompositerQuad = new FullScreenTriangle(new THREE.ShaderMaterial(e));\n            }\n        }\n        /**\n         * \n         * @param {Number} n \n         * @returns {THREE.Vector3[]}\n         */\n    generateHemisphereSamples(n) {\n            const points = [];\n            for (let k = 0; k < n; k++) {\n                const theta = 2.399963 * k;\n                const r = (Math.sqrt(k + 0.5) / Math.sqrt(n));\n                const x = r * Math.cos(theta);\n                const y = r * Math.sin(theta);\n                // Project to hemisphere\n                const z = Math.sqrt(1 - (x * x + y * y));\n                points.push(new THREE.Vector3(x, y, z));\n\n            }\n            return points;\n        }\n        /**\n         * \n         * @param {number} numSamples \n         * @param {number} numRings \n         * @returns {THREE.Vector2[]}\n         */\n    generateDenoiseSamples(numSamples, numRings) {\n        const angleStep = 2 * Math.PI * numRings / numSamples;\n        const invNumSamples = 1.0 / numSamples;\n        const radiusStep = invNumSamples;\n        const samples = [];\n        let radius = invNumSamples;\n        let angle = 0;\n        for (let i = 0; i < numSamples; i++) {\n            samples.push(new THREE.Vector2(Math.cos(angle), Math.sin(angle)).multiplyScalar(Math.pow(radius, 0.75)));\n            radius += radiusStep;\n            angle += angleStep;\n        }\n        return samples;\n    }\n    setSize(width, height) {\n        this.firstFrame();\n        this.width = width;\n        this.height = height;\n        const c = this.configuration.halfRes ? 0.5 : 1;\n        this.writeTargetInternal.setSize(width *\n            c, height *\n            c);\n        this.readTargetInternal.setSize(width *\n            c, height *\n            c);\n        this.accumulationRenderTarget.setSize(width * c, height * c);\n        if (this.configuration.halfRes) {\n            this.depthDownsampleTarget.setSize(width * c, height * c);\n        }\n        if (this.configuration.transparencyAware) {\n            this.transparencyRenderTargetDWFalse.setSize(width, height);\n            this.transparencyRenderTargetDWTrue.setSize(width, height);\n        }\n        this.outputTargetInternal.setSize(width, height);\n    }\n    setDepthTexture(depthTexture) {\n        this.depthTexture = depthTexture;\n    }\n    firstFrame() {\n        this.needsFrame = true;\n    }\n    render(renderer, inputBuffer, outputBuffer) {\n            const xrEnabled = renderer.xr.enabled;\n            renderer.xr.enabled = false;\n\n            // Copy inputBuffer to outputBuffer\n            //renderer.setRenderTarget(outputBuffer);\n            //  this.copyQuad.material.uniforms.tDiffuse.value = inputBuffer.texture;\n            //   this.copyQuad.render(renderer);\n\n            if (renderer.capabilities.logarithmicDepthBuffer && this.configuration.depthBufferType !== DepthType.Log || renderer.capabilities.reverseDepthBuffer && this.configuration.depthBufferType !== DepthType.Reverse) {\n                this.configuration.depthBufferType = renderer.capabilities.logarithmicDepthBuffer ? DepthType.Log : renderer.capabilities.reverseDepthBuffer ? DepthType.Reverse : DepthType.Default;\n                this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                this.configureDenoisePass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n            }\n            this.detectTransparency();\n            if (inputBuffer.texture.type !== this.outputTargetInternal.texture.type ||\n                inputBuffer.texture.format !== this.outputTargetInternal.texture.format\n            ) {\n                this.outputTargetInternal.texture.type = inputBuffer.texture.type;\n                this.outputTargetInternal.texture.format = inputBuffer.texture.format;\n                this.outputTargetInternal.texture.needsUpdate = true;\n            }\n            this.camera.updateMatrixWorld();\n            if (this.lastViewMatrix.equals(this.camera.matrixWorldInverse) && this.lastProjectionMatrix.equals(this.camera.projectionMatrix) && this.configuration.accumulate && !this.needsFrame) {\n                this.frame++;\n            } else {\n                if (this.configuration.accumulate) {\n                    renderer.setRenderTarget(this.accumulationRenderTarget);\n                    renderer.clear(true, true, true);\n                }\n                this.frame = 0;\n                this.needsFrame = false;\n            }\n            this.lastViewMatrix.copy(this.camera.matrixWorldInverse);\n            this.lastProjectionMatrix.copy(this.camera.projectionMatrix);\n            let gl;\n            let ext;\n            let timerQuery;\n            if (this.debugMode) {\n                gl = renderer.getContext();\n                ext = gl.getExtension('EXT_disjoint_timer_query_webgl2');\n                if (ext === null) {\n                    console.error(\"EXT_disjoint_timer_query_webgl2 not available, disabling debug mode.\");\n                    this.debugMode = false;\n                }\n            }\n            if (this.debugMode) {\n                timerQuery = gl.createQuery();\n                gl.beginQuery(ext.TIME_ELAPSED_EXT, timerQuery);\n            }\n            if (this.configuration.transparencyAware) {\n                this.renderTransparency(renderer);\n            }\n            this._r.set(this.width, this.height);\n            let trueRadius = this.configuration.aoRadius;\n            if (this.configuration.halfRes && this.configuration.screenSpaceRadius) {\n                trueRadius *= 0.5;\n            }\n            if (this.frame < 1024 / this.configuration.aoSamples) {\n                if (this.configuration.halfRes) {\n\n                    renderer.setRenderTarget(this.depthDownsampleTarget);\n                    this.depthDownsampleQuad.material.uniforms.sceneDepth.value = this.depthTexture;\n                    this.depthDownsampleQuad.material.uniforms.resolution.value = this._r;\n                    this.depthDownsampleQuad.material.uniforms[\"near\"].value = this.camera.near;\n                    this.depthDownsampleQuad.material.uniforms[\"far\"].value = this.camera.far;\n                    this.depthDownsampleQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n                    this.depthDownsampleQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n                    this.depthDownsampleQuad.material.uniforms[\"logDepth\"].value = this.configuration.logarithmicDepthBuffer;\n                    this.depthDownsampleQuad.material.uniforms[\"ortho\"].value = this.camera.isOrthographicCamera;\n                    this.depthDownsampleQuad.render(renderer);\n                }\n                this.effectShaderQuad.material.uniforms[\"sceneDiffuse\"].value = inputBuffer.texture;\n                this.effectShaderQuad.material.uniforms[\"sceneDepth\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[0] : this.depthTexture;\n                this.effectShaderQuad.material.uniforms[\"sceneNormal\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[1] : null;\n                this.effectShaderQuad.material.uniforms[\"projMat\"].value = this.camera.projectionMatrix;\n                this.effectShaderQuad.material.uniforms[\"viewMat\"].value = this.camera.matrixWorldInverse;\n                this.effectShaderQuad.material.uniforms[\"projViewMat\"].value = this.camera.projectionMatrix.clone().multiply(this.camera.matrixWorldInverse.clone());\n                this.effectShaderQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n                this.effectShaderQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n                this.effectShaderQuad.material.uniforms[\"cameraPos\"].value = this.camera.getWorldPosition(new THREE.Vector3());\n                this.effectShaderQuad.material.uniforms['biasAdjustment'].value = new THREE.Vector2(this.configuration.biasOffset, this.configuration.biasMultiplier);\n                this.effectShaderQuad.material.uniforms['resolution'].value = (this.configuration.halfRes ? this._r.clone().multiplyScalar(1 / 2).floor() : this._r);\n                this.effectShaderQuad.material.uniforms['time'].value = performance.now() / 1000;\n                this.effectShaderQuad.material.uniforms['samples'].value = this.samples;\n                this.effectShaderQuad.material.uniforms['bluenoise'].value = this.bluenoise;\n                this.effectShaderQuad.material.uniforms['radius'].value = trueRadius;\n                this.effectShaderQuad.material.uniforms['distanceFalloff'].value = this.configuration.distanceFalloff;\n                this.effectShaderQuad.material.uniforms[\"near\"].value = this.camera.near;\n                this.effectShaderQuad.material.uniforms[\"far\"].value = this.camera.far;\n                this.effectShaderQuad.material.uniforms[\"ortho\"].value = this.camera.isOrthographicCamera;\n                this.effectShaderQuad.material.uniforms[\"screenSpaceRadius\"].value = this.configuration.screenSpaceRadius;\n                this.effectShaderQuad.material.uniforms[\"frame\"].value = this.frame;\n                // Start the AO\n                renderer.setRenderTarget(this.writeTargetInternal);\n                this.effectShaderQuad.render(renderer);\n                // End the AO\n                // Start the blur\n                for (let i = 0; i < this.configuration.denoiseIterations; i++) {\n                    [this.writeTargetInternal, this.readTargetInternal] = [this.readTargetInternal, this.writeTargetInternal];\n                    this.poissonBlurQuad.material.uniforms[\"tDiffuse\"].value = this.readTargetInternal.texture;\n                    this.poissonBlurQuad.material.uniforms[\"sceneDepth\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[0] : this.depthTexture;\n                    this.poissonBlurQuad.material.uniforms[\"projMat\"].value = this.camera.projectionMatrix;\n                    this.poissonBlurQuad.material.uniforms[\"viewMat\"].value = this.camera.matrixWorldInverse;\n                    this.poissonBlurQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n                    this.poissonBlurQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n                    this.poissonBlurQuad.material.uniforms[\"cameraPos\"].value = this.camera.getWorldPosition(new THREE.Vector3());\n                    this.poissonBlurQuad.material.uniforms['resolution'].value = (this.configuration.halfRes ? this._r.clone().multiplyScalar(1 / 2).floor() : this._r);\n                    this.poissonBlurQuad.material.uniforms['time'].value = performance.now() / 1000;\n                    this.poissonBlurQuad.material.uniforms['blueNoise'].value = this.bluenoise;\n                    this.poissonBlurQuad.material.uniforms['radius'].value = this.configuration.denoiseRadius * (\n                        this.configuration.halfRes ? 1 / 2 : 1\n                    );\n                    this.poissonBlurQuad.material.uniforms['worldRadius'].value = trueRadius;\n                    this.poissonBlurQuad.material.uniforms['distanceFalloff'].value = this.configuration.distanceFalloff;\n                    this.poissonBlurQuad.material.uniforms['index'].value = i;\n                    this.poissonBlurQuad.material.uniforms['poissonDisk'].value = this.samplesDenoise;\n                    this.poissonBlurQuad.material.uniforms[\"near\"].value = this.camera.near;\n                    this.poissonBlurQuad.material.uniforms[\"far\"].value = this.camera.far;\n                    this.poissonBlurQuad.material.uniforms[\"screenSpaceRadius\"].value = this.configuration.screenSpaceRadius;\n                    renderer.setRenderTarget(this.writeTargetInternal);\n                    this.poissonBlurQuad.render(renderer);\n\n                }\n                renderer.setRenderTarget(this.accumulationRenderTarget);\n                const oldAutoClear = renderer.autoClear;\n                renderer.autoClear = false;\n                this.accumulationQuad.material.uniforms[\"tDiffuse\"].value = this.writeTargetInternal.texture;\n                this.accumulationQuad.material.uniforms[\"frame\"].value = this.frame;\n                this.accumulationQuad.render(renderer);\n                renderer.autoClear = oldAutoClear;\n            }\n            // Now, we have the blurred AO in writeTargetInternal\n            // End the blur\n            // Start the composition\n            if (this.configuration.transparencyAware) {\n                this.effectCompositerQuad.material.uniforms[\"transparencyDWFalse\"].value = this.transparencyRenderTargetDWFalse.texture;\n                this.effectCompositerQuad.material.uniforms[\"transparencyDWTrue\"].value = this.transparencyRenderTargetDWTrue.texture;\n                this.effectCompositerQuad.material.uniforms[\"transparencyDWTrueDepth\"].value = this.transparencyRenderTargetDWTrue.depthTexture;\n                this.effectCompositerQuad.material.uniforms[\"transparencyAware\"].value = true;\n            }\n            this.effectCompositerQuad.material.uniforms[\"sceneDiffuse\"].value = inputBuffer.texture;\n            this.effectCompositerQuad.material.uniforms[\"sceneDepth\"].value = this.depthTexture;\n            this.effectCompositerQuad.material.uniforms[\"aoTones\"].value = this.configuration.aoTones;\n            this.effectCompositerQuad.material.uniforms[\"near\"].value = this.camera.near;\n            this.effectCompositerQuad.material.uniforms[\"far\"].value = this.camera.far;\n            this.effectCompositerQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n            this.effectCompositerQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n            this.effectCompositerQuad.material.uniforms[\"ortho\"].value = this.camera.isOrthographicCamera;\n            this.effectCompositerQuad.material.uniforms[\"downsampledDepth\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[0] : this.depthTexture;\n            this.effectCompositerQuad.material.uniforms[\"resolution\"].value = this._r;\n            this.effectCompositerQuad.material.uniforms[\"blueNoise\"].value = this.bluenoise;\n            this.effectCompositerQuad.material.uniforms[\"intensity\"].value = this.configuration.intensity;\n            this.effectCompositerQuad.material.uniforms[\"renderMode\"].value = this.configuration.renderMode;\n            this.effectCompositerQuad.material.uniforms[\"screenSpaceRadius\"].value = this.configuration.screenSpaceRadius;\n            this.effectCompositerQuad.material.uniforms['radius'].value = trueRadius;\n            this.effectCompositerQuad.material.uniforms['distanceFalloff'].value = this.configuration.distanceFalloff;\n            this.effectCompositerQuad.material.uniforms[\"gammaCorrection\"].value = this.autosetGamma ?\n                this.renderToScreen :\n                this.configuration.gammaCorrection;\n            this.effectCompositerQuad.material.uniforms[\"tDiffuse\"].value = this.accumulationRenderTarget.texture;\n            this.effectCompositerQuad.material.uniforms[\"color\"].value =\n                this._c.copy(\n                    this.configuration.color\n                ).convertSRGBToLinear();\n            this.effectCompositerQuad.material.uniforms[\"colorMultiply\"].value = this.configuration.colorMultiply;\n            this.effectCompositerQuad.material.uniforms[\"cameraPos\"].value = this.camera.getWorldPosition(new THREE.Vector3());\n            this.effectCompositerQuad.material.uniforms[\"fog\"].value = !!this.scene.fog;\n            if (this.scene.fog) {\n                if (\n                    this.scene.fog.isFog\n                ) {\n                    this.effectCompositerQuad.material.uniforms[\"fogExp\"].value = false;\n                    this.effectCompositerQuad.material.uniforms[\"fogNear\"].value = this.scene.fog.near;\n                    this.effectCompositerQuad.material.uniforms[\"fogFar\"].value = this.scene.fog.far;\n                } else if (\n                    this.scene.fog.isFogExp2\n                ) {\n                    this.effectCompositerQuad.material.uniforms[\"fogExp\"].value = true;\n                    this.effectCompositerQuad.material.uniforms[\"fogDensity\"].value = this.scene.fog.density;\n                } else {\n                    console.error(`Unsupported fog type ${this.scene.fog.constructor.name} in SSAOPass.`);\n                }\n\n\n            }\n            renderer.setRenderTarget(\n                /* this.renderToScreen ? null :\n                 outputBuffer*/\n                this.outputTargetInternal\n            );\n            this.effectCompositerQuad.render(renderer);\n            renderer.setRenderTarget(\n                this.renderToScreen ? null :\n                outputBuffer\n            );\n            this.copyQuad.material.uniforms[\"tDiffuse\"].value = this.outputTargetInternal.texture;\n            this.copyQuad.render(renderer);\n            if (this.debugMode) {\n                gl.endQuery(ext.TIME_ELAPSED_EXT);\n                checkTimerQuery(timerQuery, gl, this);\n            }\n\n            renderer.xr.enabled = xrEnabled;\n        }\n        /**\n         * Enables the debug mode of the AO, meaning the lastTime value will be updated.\n         */\n    enableDebugMode() {\n            this.debugMode = true;\n        }\n        /**\n         * Disables the debug mode of the AO, meaning the lastTime value will not be updated.\n         */\n    disableDebugMode() {\n            this.debugMode = false;\n        }\n        /**\n         * Sets the display mode of the AO\n         * @param {\"Combined\" | \"AO\" | \"No AO\" | \"Split\" | \"Split AO\"} mode - The display mode. \n         */\n    setDisplayMode(mode) {\n            this.configuration.renderMode = [\"Combined\", \"AO\", \"No AO\", \"Split\", \"Split AO\"].indexOf(mode);\n        }\n        /**\n         * \n         * @param {\"Performance\" | \"Low\" | \"Medium\" | \"High\" | \"Ultra\"} mode \n         */\n    setQualityMode(mode) {\n        if (mode === \"Performance\") {\n            this.configuration.aoSamples = 8;\n            this.configuration.denoiseSamples = 4;\n            this.configuration.denoiseRadius = 12;\n        } else if (mode === \"Low\") {\n            this.configuration.aoSamples = 16;\n            this.configuration.denoiseSamples = 4;\n            this.configuration.denoiseRadius = 12;\n        } else if (mode === \"Medium\") {\n            this.configuration.aoSamples = 16;\n            this.configuration.denoiseSamples = 8;\n            this.configuration.denoiseRadius = 12;\n        } else if (mode === \"High\") {\n            this.configuration.aoSamples = 64;\n            this.configuration.denoiseSamples = 8;\n            this.configuration.denoiseRadius = 6;\n        } else if (mode === \"Ultra\") {\n            this.configuration.aoSamples = 64;\n            this.configuration.denoiseSamples = 16;\n            this.configuration.denoiseRadius = 6;\n        }\n\n    }\n}\nexport { N8AOPostPass };", "const BlueNoise = `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`;\n\nconst bluenoiseBits = /* @__PURE__ */ (() => Uint8Array.from(atob(BlueNoise), c => c.charCodeAt(0)))();\n\nexport default bluenoiseBits;", "import * as THREE from \"three\";\nconst version = /* @__PURE__ */ (() =>\n  parseInt(THREE.REVISION.replace(/\\D+/g, \"\")))();\n\n// NOTE: WebGLMultipleRenderTargets is removed since r172, so we implement it ourselves.\n// https://github.com/mrdoob/three.js/pull/26427\nexport const WebGLMultipleRenderTargetsCompat =\n  version >= 162\n    ? class extends THREE.WebGLRenderTarget {\n        constructor(width = 1, height = 1, count = 1, options = {}) {\n          super(width, height, { ...options, count });\n          this.isWebGLMultipleRenderTargets = true;\n        }\n        get texture() {\n          return this.textures;\n        }\n      }\n    : class extends THREE.WebGLRenderTarget {\n        constructor(width = 1, height = 1, count = 1, options = {}) {\n          super(width, height, options);\n          this.isWebGLMultipleRenderTargets = true;\n          const texture = this.texture;\n          this.texture = [];\n          for (let i = 0; i < count; i++) {\n            this.texture[i] = texture.clone();\n            this.texture[i].isRenderTargetTexture = true;\n          }\n        }\n        setSize(width, height, depth = 1) {\n          if (\n            this.width !== width ||\n            this.height !== height ||\n            this.depth !== depth\n          ) {\n            this.width = width;\n            this.height = height;\n            this.depth = depth;\n            for (let i = 0, il = this.texture.length; i < il; i++) {\n              this.texture[i].image.width = width;\n              this.texture[i].image.height = height;\n              this.texture[i].image.depth = depth;\n            }\n            this.dispose();\n          }\n          this.viewport.set(0, 0, width, height);\n          this.scissor.set(0, 0, width, height);\n        }\n        copy(source) {\n          this.dispose();\n          this.width = source.width;\n          this.height = source.height;\n          this.depth = source.depth;\n          this.scissor.copy(source.scissor);\n          this.scissorTest = source.scissorTest;\n          this.viewport.copy(source.viewport);\n          this.depthBuffer = source.depthBuffer;\n          this.stencilBuffer = source.stencilBuffer;\n          if (source.depthTexture !== null)\n            this.depthTexture = source.depthTexture.clone();\n          this.texture.length = 0;\n          for (let i = 0, il = source.texture.length; i < il; i++) {\n            this.texture[i] = source.texture[i].clone();\n            this.texture[i].isRenderTargetTexture = true;\n          }\n          return this;\n        }\n      };\n"], "names": [], "version": 3, "file": "N8AO.js.map"}